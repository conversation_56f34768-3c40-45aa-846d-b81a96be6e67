<!DOCTYPE html>
<html lang="pl" dir="ltr" class="scroll-smooth" data-default-appearance="dark"
  data-auto-appearance="false"><head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="utf-8" />
  
  <meta http-equiv="content-language" content="pl" />
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  
  <title>Meetup #59 &middot; Python Łódź</title>
  <meta name="title" content="Meetup #59 &middot; Python Łódź" />
  
  
  
  
  
  <link rel="canonical" href="http://localhost:1313/spotkania/59/" />
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <link type="text/css" rel="stylesheet" href="/css/main.bundle.min.34d7373bbf62125b4a19bef366fe4fdef50fae18b4c53e765a59480da810b01e886160b5ff317f1ae96cef62f058c77940a42f4e588c0ab95e549205d24caab0.css"
    integrity="sha512-NNc3O79iEltKGb7zZv5P3vUPrhi0xT52WllIDagQsB6IYWC1/zF/Guls72LwWMd5QKQvTliMCrleVJIF0kyqsA==" />
  
  
  <script type="text/javascript" src="/js/appearance.min.516a16745bea5a9bd011138d254cc0fd3973cd55ce6e15f3dec763e7c7c2c7448f8fe7b54cca811cb821b0c7e12cd161caace1dd794ac3d34d40937cbcc9ee12.js"
    integrity="sha512-UWoWdFvqWpvQERONJUzA/TlzzVXObhXz3sdj58fCx0SPj&#43;e1TMqBHLghsMfhLNFhyqzh3XlKw9NNQJN8vMnuEg=="></script>
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <script defer type="text/javascript" id="script-bundle" src="/js/main.bundle.min.3ad74b33440334ca29f8801ec6dac1f314951cf580e503d49631816b48d1f28d03543275f92b651565b48045b90731f47595b1396214b8b6aa11c6990f867f7e.js"
    integrity="sha512-OtdLM0QDNMop&#43;IAextrB8xSVHPWA5QPUljGBa0jR8o0DVDJ1&#43;StlFWW0gEW5BzH0dZWxOWIUuLaqEcaZD4Z/fg==" data-copy="" data-copied=""></script>
  
  
  
  <script src="/lib/zoom/zoom.min.37d2094687372da3f7343a221a470f6b8806f7891aa46a5a03966af7f0ebd38b9fe536cb154e6ad28f006d184b294525a7c4054b6bbb4be62d8b453b42db99bd.js" integrity="sha512-N9IJRoc3LaP3NDoiGkcPa4gG94kapGpaA5Zq9/Dr04uf5TbLFU5q0o8AbRhLKUUlp8QFS2u7S&#43;Yti0U7QtuZvQ=="></script>
  
  
  
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
  <link rel="manifest" href="/site.webmanifest" />
  
  
  
  
  
  
  
  
  <meta property="og:url" content="http://localhost:1313/spotkania/59/">
  <meta property="og:site_name" content="Python Łódź">
  <meta property="og:title" content="Meetup #59">
  <meta property="og:locale" content="pl">
  <meta property="og:type" content="article">
    <meta property="article:section" content="spotkania">
    <meta property="article:published_time" content="2025-07-30T18:00:00+02:00">
    <meta property="article:modified_time" content="2025-07-30T18:00:00+02:00">
    <meta property="og:image" content="http://localhost:1313/spotkania/59/featured.png">

  
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:image" content="http://localhost:1313/spotkania/59/featured.png">
  <meta name="twitter:title" content="Meetup #59">

  
  <script type="application/ld+json">
  [{
    "@context": "https://schema.org",
    "@type": "Article",
    "articleSection": "Spotkania",
    "name": "Meetup #59",
    "headline": "Meetup #59",
    
    
    "inLanguage": "pl",
    "url" : "http:\/\/localhost:1313\/spotkania\/59\/",
    "author" : {
      "@type": "Person",
      "name": "Python Łódź"
    },
    "copyrightYear": "2025",
    "dateCreated": "2025-07-30T18:00:00\u002b02:00",
    "datePublished": "2025-07-30T18:00:00\u002b02:00",
    
    "dateModified": "2025-07-30T18:00:00\u002b02:00",
    
    
    
    "mainEntityOfPage": "true",
    "wordCount": "401"
  }]
  </script>


  
  
  <meta name="author" content="Python Łódź" />
  
  
  
  <link href="https://discord.gg/jbvWBMufEf" rel="me" />
  
  
  <link href="https://www.facebook.com/pythonlodz" rel="me" />
  
  
  <link href="https://github.com/python-lodz" rel="me" />
  
  
  <link href="https://www.linkedin.com/company/python-lodz" rel="me" />
  
  
  <link href="https://www.youtube.com/@pythonlodz" rel="me" />
  
  
  
  

<script src="/lib/jquery/jquery.slim.min.b0dca576e87d7eaa5850ae4e61759c065786cdb6489d68fcc82240539eebd5da522bdb4fda085ffd245808c8fe2acb2516408eb774ef26b5f6015fc6737c0ea8.js" integrity="sha512-sNylduh9fqpYUK5OYXWcBleGzbZInWj8yCJAU57r1dpSK9tP2ghf/SRYCMj&#43;KsslFkCOt3TvJrX2AV/Gc3wOqA=="></script>






















  
  

<script async src="https://www.googletagmanager.com/gtag/js?id=G-SL732ZDGYL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-SL732ZDGYL');
</script>



  
  
  



<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '1849865132461926');
fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none"
src="https://www.facebook.com/tr?id=1849865132461926&ev=PageView&noscript=1"
/></noscript>



  
  <meta name="theme-color"/>
  
  
</head>
<body
  class="flex flex-col h-screen px-6 m-auto text-lg leading-7 max-w-7xl bg-neutral text-neutral-900 dark:bg-neutral-800 dark:text-neutral sm:px-14 md:px-24 lg:px-32 scrollbar-thin scrollbar-track-neutral-200 scrollbar-thumb-neutral-400 dark:scrollbar-track-neutral-800 dark:scrollbar-thumb-neutral-600">
  <div id="the-top" class="absolute flex self-center">
    <a class="px-3 py-1 text-sm -translate-y-8 rounded-b-lg bg-primary-200 focus:translate-y-0 dark:bg-neutral-600"
      href="#main-content"><span
        class="font-bold text-primary-600 ltr:pr-2 rtl:pl-2 dark:text-primary-400">&darr;</span>Przewiń do głównej treści</a>
  </div>
  
  
  <div style="padding-left:0;padding-right:0;padding-top:2px;padding-bottom:3px"
    class="main-menu flex items-center justify-between px-4 py-6 sm:px-6 md:justify-start gap-x-3">
    
    <div class="flex flex-1 items-center justify-between">
        <nav class="flex space-x-3">

            
            <a href="/" class="text-base font-medium text-gray-500 hover:text-gray-900">Python Łódź</a>
            

        </nav>
        <nav class="hidden md:flex items-center gap-x-5 md:ml-12 h-12">

            
            
            
  <a href="/sponsorzy/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
    
    <p class="text-base font-medium" title="Sponsorzy">
        Sponsorzy
    </p>
</a>



            
            
  <a href="/spotkania/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
    
    <p class="text-base font-medium" title="Spotkania">
        Spotkania
    </p>
</a>



            
            

            


            
            <button id="search-button" aria-label="Search" class="text-base hover:text-primary-600 dark:hover:text-primary-400"
                title="">
                

  <span class="relative block icon">
    <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" class="svg-inline--fa fa-search fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"/></svg>

  </span>


            </button>
            


            
            

        </nav>
        <div class="flex md:hidden items-center gap-x-5 md:ml-12 h-12">

            <span></span>

            


            
            <button id="search-button-mobile" aria-label="Search" class="text-base hover:text-primary-600 dark:hover:text-primary-400"
                title="">
                

  <span class="relative block icon">
    <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" class="svg-inline--fa fa-search fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"/></svg>

  </span>


            </button>
            

            
            

        </div>
    </div>
    <div class="-my-2 md:hidden">

        <label id="menu-button" class="block">
            
            <div class="cursor-pointer hover:text-primary-600 dark:hover:text-primary-400">
                

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 96C0 78.33 14.33 64 32 64H416C433.7 64 448 78.33 448 96C448 113.7 433.7 128 416 128H32C14.33 128 0 113.7 0 96zM0 256C0 238.3 14.33 224 32 224H416C433.7 224 448 238.3 448 256C448 273.7 433.7 288 416 288H32C14.33 288 0 273.7 0 256zM416 448H32C14.33 448 0 433.7 0 416C0 398.3 14.33 384 32 384H416C433.7 384 448 398.3 448 416C448 433.7 433.7 448 416 448z"/></svg>

  </span>


            </div>
            <div id="menu-wrapper" style="padding-top:5px;"
                class="fixed inset-0 z-30 invisible w-screen h-screen m-0 overflow-auto transition-opacity opacity-0 cursor-default bg-neutral-100/50 backdrop-blur-sm dark:bg-neutral-900/50">
                <ul
                    class="flex space-y-2 mt-3 flex-col items-end w-full px-6 py-6 mx-auto overflow-visible list-none ltr:text-right rtl:text-left max-w-7xl">

                    <li id="menu-close-button">
                        <span
                            class="cursor-pointer inline-block align-text-bottom hover:text-primary-600 dark:hover:text-primary-400">

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M310.6 361.4c12.5 12.5 12.5 32.75 0 45.25C304.4 412.9 296.2 416 288 416s-16.38-3.125-22.62-9.375L160 301.3L54.63 406.6C48.38 412.9 40.19 416 32 416S15.63 412.9 9.375 406.6c-12.5-12.5-12.5-32.75 0-45.25l105.4-105.4L9.375 150.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L160 210.8l105.4-105.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-105.4 105.4L310.6 361.4z"/></svg>

  </span>

</span>
                    </li>

                    

                    
  <li class="mt-1">
    <a href="/sponsorzy/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
        
        <p class="text-bg font-bg" title="Sponsorzy">
            Sponsorzy
        </p>
    </a>
</li>




                    

                    
  <li class="mt-1">
    <a href="/spotkania/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
        
        <p class="text-bg font-bg" title="Spotkania">
            Spotkania
        </p>
    </a>
</li>




                    

                </ul>
                
                

            </div>
        </label>
    </div>
</div>




<script>
    (function () {
        var $mainmenu = $('.main-menu');
        var path = window.location.pathname;
        $mainmenu.find('a[href="' + path + '"]').each(function (i, e) {
            $(e).children('p').addClass('active');
        });
    })();
</script>


  
  <div class="relative flex flex-col grow">
    <main id="main-content" class="grow">
      


<article>
  

  <header id="single_header" class="mt-5 max-w-prose">
    
    <h1 class="mt-0 text-4xl font-extrabold text-neutral-900 dark:text-neutral">
      Meetup #59
    </h1>
    <div class="mt-1 mb-6 text-base text-neutral-500 dark:text-neutral-400 print:hidden">
      





  
  

























<div class="flex flex-row flex-wrap items-center">
  
  

  
  
</div>








    </div>

    
    
    
    
    

    

    
      
      

      

      

    

  </header>
  
  <section class="flex flex-col max-w-full mt-0 prose dark:prose-invert lg:flex-row">
    
     <div
      class="order-first lg:ml-auto px-0 lg:order-last ltr:lg:pl-8 rtl:lg:pr-8">
      <div class="toc ltr:pl-5 rtl:pr-5 print:hidden lg:sticky lg:top-10">

         <details open id="TOCView"
  class="toc-right mt-0 overflow-y-scroll overscroll-contain scrollbar-thin scrollbar-track-neutral-200 scrollbar-thumb-neutral-400 dark:scrollbar-track-neutral-800 dark:scrollbar-thumb-neutral-600 rounded-lg ltr:-ml-5 ltr:pl-5 rtl:-mr-5 rtl:pr-5 hidden lg:block">
  <summary
    class="block py-1 text-lg font-semibold cursor-pointer bg-neutral-100 text-neutral-800 ltr:-ml-5 ltr:pl-5 rtl:-mr-5 rtl:pr-5 dark:bg-neutral-700 dark:text-neutral-100 lg:hidden">
    Spis treści
  </summary>
  <div
    class="min-w-[220px] py-2 border-dotted ltr:-ml-5 ltr:border-l ltr:pl-5 rtl:-mr-5 rtl:border-r rtl:pr-5 dark:border-neutral-600">
    <nav id="TableOfContents">
  <ul>
    <li><a href="#informacje">Informacje</a></li>
    <li><a href="#lightning-talk">Lightning Talk</a>
      <ul>
        <li><a href="#-nagroda">🎉 <strong>Nagroda!</strong></a></li>
        <li><a href="#czym-jest-lightning-talk">Czym jest Lightning Talk?</a></li>
        <li><a href="#zasady">Zasady</a></li>
        <li><a href="#zgłoszenia-do-lightning-talka">Zgłoszenia do Lightning Talka</a></li>
      </ul>
    </li>
    <li><a href="#open-spaces">Open Spaces</a>
      <ul>
        <li><a href="#jak-to-działa">Jak to działa?</a></li>
        <li><a href="#dlaczego-warto">Dlaczego warto?</a></li>
      </ul>
    </li>
    <li><a href="#sponsorzy">Sponsorzy</a></li>
  </ul>
</nav>
  </div>
</details>
<details class="toc-inside mt-0 overflow-hidden rounded-lg ltr:-ml-5 ltr:pl-5 rtl:-mr-5 rtl:pr-5 lg:hidden">
  <summary
    class="py-1 text-lg font-semibold cursor-pointer bg-neutral-100 text-neutral-800 ltr:-ml-5 ltr:pl-5 rtl:-mr-5 rtl:pr-5 dark:bg-neutral-700 dark:text-neutral-100 lg:hidden">
    Spis treści
  </summary>
  <div
    class="py-2 border-dotted border-neutral-300 ltr:-ml-5 ltr:border-l ltr:pl-5 rtl:-mr-5 rtl:border-r rtl:pr-5 dark:border-neutral-600">
    <nav id="TableOfContents">
  <ul>
    <li><a href="#informacje">Informacje</a></li>
    <li><a href="#lightning-talk">Lightning Talk</a>
      <ul>
        <li><a href="#-nagroda">🎉 <strong>Nagroda!</strong></a></li>
        <li><a href="#czym-jest-lightning-talk">Czym jest Lightning Talk?</a></li>
        <li><a href="#zasady">Zasady</a></li>
        <li><a href="#zgłoszenia-do-lightning-talka">Zgłoszenia do Lightning Talka</a></li>
      </ul>
    </li>
    <li><a href="#open-spaces">Open Spaces</a>
      <ul>
        <li><a href="#jak-to-działa">Jak to działa?</a></li>
        <li><a href="#dlaczego-warto">Dlaczego warto?</a></li>
      </ul>
    </li>
    <li><a href="#sponsorzy">Sponsorzy</a></li>
  </ul>
</nav>
  </div>
</details>

<script>

  var margin = 200;
  var marginError = 50;

  (function () {
    var $window = $(window);
    var $toc = $('#TOCView');
    var tocHeight = $toc.height();

    function onResize() {
      var windowAndMarginHeight = $window.height() - margin;
      if(tocHeight >= windowAndMarginHeight) {
        $toc.css("overflow-y", "scroll")
        $toc.css("max-height", (windowAndMarginHeight + marginError) + "px")
      } else {
        $toc.css("overflow-y", "hidden")
        $toc.css("max-height", "9999999px")
      }
    }

    $window.on('resize', onResize);
    $(document).ready(onResize);
  })();



  (function () {
    var $toc = $('#TableOfContents');
    if ($toc.length > 0) {
      var $window = $(window);

      function onScroll() {
        var currentScroll = $window.scrollTop();
        var h = $('.anchor');
        var id = "";
        h.each(function (i, e) {
          e = $(e);
          if (e.offset().top - $(window).height()/3 <= currentScroll) {
            id = decodeURIComponent(e.attr('id'));
          }
        });
        var active = $toc.find('a.active');      
        if (active.length == 1 && active.eq(0).attr('href') == '#' + id) return true;

        active.each(function (i, e) {
          
            $(e).removeClass('active');
          
        });
        $toc.find('a[href="#' + id + '"]').addClass('active')
        $toc.find('a[href="#' + id + '"]').parentsUntil('#TableOfContents').each(function (i, e) {
          $(e).children('a').parents('ul').show();          
        });
      }

      $window.on('scroll', onScroll);
      $(document).ready(function () {
        
        onScroll();
      });
    }
  })();


</script>
   </div>
      </div>
      

      <div class="min-w-0 min-h-0 max-w-fit">
        
        


        <div class="article-content max-w-prose mb-20">
          <img src="featured.png" alt="Infographic" />


<h2 class="relative group">Informacje 
    <div id="informacje" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#informacje" aria-label="Kotwica">#</a>
    </span>        
    
</h2>
<p><strong>📅 data:</strong> 2025-07-30</br>
<strong>🕕 godzina:</strong> 18:00</br>
<strong>📍 miejsce:</strong> IndieBI, Piotrkowska 157A, budynek Hi Piotrkowska</br>
➡️ <a href="https://www.meetup.com/python-lodz/events/309046591" target="_blank"><strong>LINK DO ZAPISÓW</strong></a> ⬅️</p>


<h2 class="relative group">Lightning Talk 
    <div id="lightning-talk" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#lightning-talk" aria-label="Kotwica">#</a>
    </span>        
    
</h2>
<p>Chcesz spróbować swoich sił na scenie?
Zgłoś swój <strong>Lightning Talk</strong> już dziś przez formularz - pomoże nam to lepiej zaplanować spotkanie.
Możesz też zgłosić się w dniu meetup’u prowadzącemu - czyli <strong>30.07.2025 o 18:00</strong>.</p>
<p>Ostateczne potwierdzenie wystąpienia robimy tuż przed prezentacją.
Jeśli nie zdążysz przygotować tematu albo coś Ci wypadnie - nic się nie stało!</p>


<h3 class="relative group">🎉 <strong>Nagroda!</strong> 
    <div id="-nagroda" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#-nagroda" aria-label="Kotwica">#</a>
    </span>        
    
</h3>
<p>Dla osób, które poprowadzą Lightning Talk, będą do rozdania <strong>dwie wejściówki na tegoroczny PyConPL!</strong></p>


<h3 class="relative group">Czym jest Lightning Talk? 
    <div id="czym-jest-lightning-talk" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#czym-jest-lightning-talk" aria-label="Kotwica">#</a>
    </span>        
    
</h3>
<p>Krótka prezentacja na maksymalnie <strong>5 minut</strong>, w której możesz opowiedzieć o czym tylko chcesz (oczywiście najlepiej, jeśli ma to związek z Pythonem i naszą społecznością).</p>


<h3 class="relative group">Zasady 
    <div id="zasady" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#zasady" aria-label="Kotwica">#</a>
    </span>        
    
</h3>
<ul>
<li><strong>5 minut</strong> na wypowiedź. Nie musisz wykorzystać pełnego czasu - możesz mówić minutę, jeśli chcesz.</li>
<li><strong>Dowolna forma</strong> - możesz mieć slajdy albo nie. Pokaż stronę projektu, zrób mini live demo, opowiedz historię, a jeśli chcesz - możesz nawet zatańczyć!</li>
<li><strong>Tematyka</strong> - Python, wszystko co z nim związane, ale też:
<ul>
<li>własne biblioteki,</li>
<li>ciekawe lokalne inicjatywy,</li>
<li>inspirujące fakapy z pracy,</li>
<li>projekty po godzinach.</li>
</ul>
</li>
<li>W ostatnich <strong>10 sekundach</strong> publiczność zacznie <strong>cicho klaskać</strong>, żeby dać Ci znać, że zbliżasz się do limitu. Po 5 minutach zaczynamy klaskać głośno - Twój czas się kończy!</li>
<li>Zero spiny! Nie oceniamy, wspieramy się nawzajem i bawimy się dobrze.</li>
</ul>


<h3 class="relative group">Zgłoszenia do Lightning Talka 
    <div id="zg%C5%82oszenia-do-lightning-talka" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#zg%C5%82oszenia-do-lightning-talka" aria-label="Kotwica">#</a>
    </span>        
    
</h3>
<p>Zgłoś swój Lightning Talk już dziś przez <a href="https://docs.google.com/forms/d/e/1FAIpQLSd14qYEV4946e_sZOToWV9cE1bEqO0BYZF3tYlzdL2s1YIBJg/viewform?usp=dialog" target="_blank"><strong>formularz</strong></a> - pomoże nam to lepiej zaplanować spotkanie.
Możesz też zgłosić się w dniu meetup’u prowadzącemu - czyli <strong>30.07.2025 o 18:00</strong>.</p>


<h2 class="relative group">Open Spaces 
    <div id="open-spaces" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#open-spaces" aria-label="Kotwica">#</a>
    </span>        
    
</h2>
<p>Po Lightning Talkach zapraszamy na <strong>Open Spaces</strong>!
To luźna i otwarta forma dyskusji, w której każdy może zaproponować temat do omówienia.
Masz coś, o czym chcesz pogadać? Widzisz problem, który warto rozkminić w grupie? Śmiało!</p>


<h3 class="relative group">Jak to działa? 
    <div id="jak-to-dzia%C5%82a" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#jak-to-dzia%C5%82a" aria-label="Kotwica">#</a>
    </span>        
    
</h3>
<ul>
<li><strong>Każdy może zgłosić temat</strong>, który go interesuje.</li>
<li>Robimy <strong>głosowanie</strong> na wszystkie propozycje - podnosisz rękę dowolną ilość razy, na każdy temat, który według Ciebie jest ciekawy.</li>
<li>Dzielimy się na <strong>grupy</strong> i rozmawiamy o najpopularniejszych tematach.</li>
<li>Możesz <strong>zmieniać grupy</strong> - jeśli jeden temat się wyczerpie, wskakujesz do innej dyskusji.</li>
<li>Zero sztywnej agendy - liczy się Twoje zaangażowanie i wymiana doświadczeń.</li>
</ul>


<h3 class="relative group">Dlaczego warto? 
    <div id="dlaczego-warto" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#dlaczego-warto" aria-label="Kotwica">#</a>
    </span>        
    
</h3>
<p><strong>Open Spaces</strong> to świetna okazja, żeby:</p>
<ul>
<li>pogłębić tematy poruszone na Lightning Talkach,</li>
<li>zadać pytania, które nie zmieściły się w prezentacji,</li>
<li>poznać ludzi, którzy mają podobne wyzwania i pomysły.</li>
</ul>
<p>Do zobaczenia na scenie!</p>


<h2 class="relative group">Sponsorzy 
    <div id="sponsorzy" class="anchor"></div>
    
    <span
        class="absolute top-0 w-6 transition-opacity opacity-0 ltr:-left-6 rtl:-right-6 not-prose group-hover:opacity-100">
        <a class="group-hover:text-primary-300 dark:group-hover:text-neutral-700"
            style="text-decoration-line: none !important;" href="#sponsorzy" aria-label="Kotwica">#</a>
    </span>        
    
</h2>

<section class="space-y-10 w-full">
    
    
        
            






















  <a class="flex flex-wrap article border border-neutral-200 dark:border-neutral-700 border-2 rounded-md overflow-hidden" href="/sponsorzy/indiebi/">
    
        
        <div class="w-full md:w-auto h-full thumbnail nozoom " style="background-image:url(/images/sponsors/indiebi_featured_hu_ac0a417fc66d2579.png);"></div>
        
      <div class=" p-4">
      <div class="items-center text-left text-xl font-semibold">
        
        <div class="font-bold text-xl text-neutral-800 decoration-primary-500 hover:underline hover:underline-offset-2 dark:text-neutral"
          href="/sponsorzy/indiebi/">IndieBI</div>
        
        
        
      </div>
      <div class="text-sm text-neutral-500 dark:text-neutral-400">
        





























<div class="flex flex-row flex-wrap items-center">
  
  

  
  
</div>








      </div>
      
    </div>
  </a>

        
    
</section>

<section class="space-y-10 w-full">
    
    
        
            






















  <a class="flex flex-wrap article border border-neutral-200 dark:border-neutral-700 border-2 rounded-md overflow-hidden" href="/sponsorzy/sunscrapers/">
    
        
        <div class="w-full md:w-auto h-full thumbnail nozoom " style="background-image:url(/images/sponsors/sunscrapers_featured_hu_3cbae066e431d9c3.png);"></div>
        
      <div class=" p-4">
      <div class="items-center text-left text-xl font-semibold">
        
        <div class="font-bold text-xl text-neutral-800 decoration-primary-500 hover:underline hover:underline-offset-2 dark:text-neutral"
          href="/sponsorzy/sunscrapers/">Sunscrapers</div>
        
        
        
      </div>
      <div class="text-sm text-neutral-500 dark:text-neutral-400">
        





























<div class="flex flex-row flex-wrap items-center">
  
  

  
  
</div>








      </div>
      
    </div>
  </a>

        
    
</section>

          
          
          
        </div>
        
        

        
        
  
  <section class="flex flex-row flex-wrap justify-center pt-4 text-xl">
    
      
      <a
      target="_blank"
      class="m-1 rounded bg-neutral-300 p-1.5 text-neutral-700 hover:bg-primary-500 hover:text-neutral dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-primary-400 dark:hover:text-neutral-800"
      href="https://www.linkedin.com/shareArticle?mini=true&amp;url=http://localhost:1313/spotkania/59/&amp;title=Meetup%20#59"
      title="Udostępnij na LinkedInie"
      aria-label="Udostępnij na LinkedInie"
      >
      

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"/></svg>

  </span>


    </a>
      
    
      
      <a
      target="_blank"
      class="m-1 rounded bg-neutral-300 p-1.5 text-neutral-700 hover:bg-primary-500 hover:text-neutral dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-primary-400 dark:hover:text-neutral-800"
      href="https://twitter.com/intent/tweet/?url=http://localhost:1313/spotkania/59/&amp;text=Meetup%20#59"
      title="Tweetuj na Twitterze"
      aria-label="Tweetuj na Twitterze"
      >
      

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"/></svg>
  </span>


    </a>
      
    
      
      <a
      target="_blank"
      class="m-1 rounded bg-neutral-300 p-1.5 text-neutral-700 hover:bg-primary-500 hover:text-neutral dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-primary-400 dark:hover:text-neutral-800"
      href="https://s2f.kytta.dev/?text=Meetup%20#59%20http://localhost:1313/spotkania/59/"
      title=""
      aria-label=""
      >
      

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.54 102.54 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5zm-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg>

  </span>


    </a>
      
    
      
      <a
      target="_blank"
      class="m-1 rounded bg-neutral-300 p-1.5 text-neutral-700 hover:bg-primary-500 hover:text-neutral dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-primary-400 dark:hover:text-neutral-800"
      href="https://reddit.com/submit/?url=http://localhost:1313/spotkania/59/&amp;resubmit=true&amp;title=Meetup%20#59"
      title="Prześlij na Reddita"
      aria-label="Prześlij na Reddita"
      >
      

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M201.5 305.5c-13.8 0-24.9-11.1-24.9-24.6 0-13.8 11.1-24.9 24.9-24.9 13.6 0 24.6 11.1 24.6 24.9 0 13.6-11.1 24.6-24.6 24.6zM504 256c0 137-111 248-248 248S8 393 8 256 119 8 256 8s248 111 248 248zm-132.3-41.2c-9.4 0-17.7 3.9-23.8 10-22.4-15.5-52.6-25.5-86.1-26.6l17.4-78.3 55.4 12.5c0 13.6 11.1 24.6 24.6 24.6 13.8 0 24.9-11.3 24.9-24.9s-11.1-24.9-24.9-24.9c-9.7 0-18 5.8-22.1 13.8l-61.2-13.6c-3-.8-6.1 1.4-6.9 4.4l-19.1 86.4c-33.2 1.4-63.1 11.3-85.5 26.8-6.1-6.4-14.7-10.2-24.1-10.2-34.9 0-46.3 46.9-14.4 62.8-1.1 5-1.7 10.2-1.7 15.5 0 52.6 59.2 95.2 132 95.2 73.1 0 132.3-42.6 132.3-95.2 0-5.3-.6-10.8-1.9-15.8 31.3-16 19.8-62.5-14.9-62.5zM302.8 331c-18.2 18.2-76.1 17.9-93.6 0-2.2-2.2-6.1-2.2-8.3 0-2.5 2.5-2.5 6.4 0 8.6 22.8 22.8 87.3 22.8 110.2 0 2.5-2.2 2.5-6.1 0-8.6-2.2-2.2-6.1-2.2-8.3 0zm7.7-75c-13.6 0-24.6 11.1-24.6 24.9 0 13.6 11.1 24.6 24.6 24.6 13.8 0 24.9-11.1 24.9-24.6 0-13.8-11-24.9-24.9-24.9z"/></svg>

  </span>


    </a>
      
    
      
      <a
      target="_blank"
      class="m-1 rounded bg-neutral-300 p-1.5 text-neutral-700 hover:bg-primary-500 hover:text-neutral dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-primary-400 dark:hover:text-neutral-800"
      href="https://www.facebook.com/sharer/sharer.php?u=http://localhost:1313/spotkania/59/&amp;quote=Meetup%20#59"
      title="Udostępnij na Facebooku"
      aria-label="Udostępnij na Facebooku"
      >
      

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"/></svg>

  </span>


    </a>
      
    
  </section>


          
      </div>
     
      
      
        
        
          
          
        
      <script>
        var oid = "views_spotkania\/59\/index.md"
        var oid_likes = "likes_spotkania\/59\/index.md"
      </script>
      
      
      <script type="text/javascript" src="/js/page.min.0860cf4e04fa2d72cc33ddba263083464d48f67de06114529043cb4623319efed4f484fd7f1730df5abea0e2da6f3538855634081d02f2d6e920b956f063e823.js" integrity="sha512-CGDPTgT6LXLMM926JjCDRk1I9n3gYRRSkEPLRiMxnv7U9IT9fxcw31q&#43;oOLabzU4hVY0CB0C8tbpILlW8GPoIw=="></script>
      
  
    </section>
  <footer class="pt-8 max-w-prose print:hidden">

    
  
    
    
    
      
      
    
    <div class="pt-8">
      <hr class="border-dotted border-neutral-300 dark:border-neutral-600" />
      <div class="flex justify-between pt-3">
        <span>
          
        </span>
        <span>
          
            <a class="flex text-right group ml-3" href="/spotkania/58/">
              <span class="flex flex-col">
                <span
                  class="mt-[0.1rem] leading-6 group-hover:underline group-hover:decoration-primary-500"
                  >Meetup #58</span
                >
                <span class="mt-[0.1rem] text-xs text-neutral-500 dark:text-neutral-400">
                  
                </span>
              </span>
              <span
                class="ml-3 text-neutral-700 group-hover:text-primary-600 ltr:inline rtl:hidden dark:text-neutral dark:group-hover:text-primary-400"
                >&rarr;</span
              >
              <span
                class="mr-3 text-neutral-700 group-hover:text-primary-600 ltr:hidden rtl:inline dark:text-neutral dark:group-hover:text-primary-400"
                >&larr;</span
              >
            </a>
          
        </span>
      </div>
    </div>
  


    
  </footer>
</article>

      <div id="top-scroller" class="pointer-events-none absolute top-[110vh] bottom-0 w-12 ltr:right-0 rtl:left-0">
  <a href="#the-top"
    class="pointer-events-auto sticky top-[calc(100vh-5.5rem)] flex h-12 w-12 mb-16 items-center justify-center rounded-full bg-neutral/50 text-xl text-neutral-700 hover:text-primary-600 dark:bg-neutral-800/50 dark:text-neutral dark:hover:text-primary-400"
    aria-label="Przewiń do góry" title="Przewiń do góry">
    &uarr;
  </a>
</div>
    </main><footer id="site-footer" class="py-10 print:hidden">
  
  
    
    <nav class="flex flex-row pb-4 text-base font-medium text-neutral-500 dark:text-neutral-400">
      <ul class="flex flex-col list-none sm:flex-row">
        
        <li class="flex mb-1 ltr:text-right rtl:text-left sm:mb-0 ltr:sm:mr-7 ltr:sm:last:mr-0 rtl:sm:ml-7 rtl:sm:last:ml-0">
          <a class="decoration-primary-500 hover:underline hover:decoration-2 hover:underline-offset-2 flex items-center" href="/regulamin-uczestnictwa/"
            title="Regulamin uczestnictwa w spotkaniach Python Łódź">
            
            Regulamin uczestnictwa
          </a>
        </li>
        
        <li class="flex mb-1 ltr:text-right rtl:text-left sm:mb-0 ltr:sm:mr-7 ltr:sm:last:mr-0 rtl:sm:ml-7 rtl:sm:last:ml-0">
          <a class="decoration-primary-500 hover:underline hover:decoration-2 hover:underline-offset-2 flex items-center" href="/polityka-cookies/"
            title="Polityka Cookies">
            
            Polityka Cookies
          </a>
        </li>
        
        <li class="flex mb-1 ltr:text-right rtl:text-left sm:mb-0 ltr:sm:mr-7 ltr:sm:last:mr-0 rtl:sm:ml-7 rtl:sm:last:ml-0">
          <a class="decoration-primary-500 hover:underline hover:decoration-2 hover:underline-offset-2 flex items-center" href="/prelegenci-przetwarzanie-danych/"
            title="Informacja o przetwarzaniu danych osobowych prelegenta spotkań Python Łódź">
            
            Przetwarzanie danych osobowych prelegenta
          </a>
        </li>
        
      </ul>
    </nav>
    
  
  <div class="flex items-center justify-between">

    
    
    <p class="text-sm text-neutral-500 dark:text-neutral-400">
      &copy;
      2025
      Python Łódź
    </p>
    

    
    
    <p class="text-xs text-neutral-500 dark:text-neutral-400">
      
      
      Powered by <a class="hover:underline hover:decoration-primary-400 hover:text-primary-500"
        href="https://gohugo.io/" target="_blank" rel="noopener noreferrer">Hugo</a> &amp; <a class="hover:underline hover:decoration-primary-400 hover:text-primary-500"
        href="https://blowfish.page/" target="_blank" rel="noopener noreferrer">Blowfish</a>
    </p>
    

  </div>
  <script>
    
    mediumZoom(document.querySelectorAll("img:not(.nozoom)"), {
      margin: 24,
      background: 'rgba(0,0,0,0.5)',
      scrollOffset: 0,
    })
    
  </script>
  
  
  <script type="text/javascript" src="/js/process.min.ee03488f19c93c2efb199e2e3014ea5f3cb2ce7d45154adb3399a158cac27ca52831db249ede5bb602700ef87eb02434139de0858af1818ab0fb4182472204a4.js" integrity="sha512-7gNIjxnJPC77GZ4uMBTqXzyyzn1FFUrbM5mhWMrCfKUoMdsknt5btgJwDvh&#43;sCQ0E53ghYrxgYqw&#43;0GCRyIEpA=="></script>
  
  
</footer>
<div
  id="search-wrapper"
  class="invisible fixed inset-0 flex h-screen w-screen cursor-default flex-col bg-neutral-500/50 p-4 backdrop-blur-sm dark:bg-neutral-900/50 sm:p-6 md:p-[10vh] lg:p-[12vh]"
  data-url="http://localhost:1313/"
  style="z-index:500"
>
  <div
    id="search-modal"
    class="flex flex-col w-full max-w-3xl min-h-0 mx-auto border rounded-md shadow-lg top-20 border-neutral-200 bg-neutral dark:border-neutral-700 dark:bg-neutral-800"
  >
    <header class="relative z-10 flex items-center justify-between flex-none px-2">
      <form class="flex items-center flex-auto min-w-0">
        <div class="flex items-center justify-center w-8 h-8 text-neutral-400">
          

  <span class="relative block icon">
    <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" class="svg-inline--fa fa-search fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"/></svg>

  </span>


        </div>
        <input
          type="search"
          id="search-query"
          class="flex flex-auto h-12 mx-1 bg-transparent appearance-none focus:outline-dotted focus:outline-2 focus:outline-transparent"
          placeholder="Szukaj"
          tabindex="0"
        />
      </form>
      <button
        id="close-search-button"
        class="flex items-center justify-center w-8 h-8 text-neutral-700 hover:text-primary-600 dark:text-neutral dark:hover:text-primary-400"
        title="Zamknij (Esc)"
      >
        

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M310.6 361.4c12.5 12.5 12.5 32.75 0 45.25C304.4 412.9 296.2 416 288 416s-16.38-3.125-22.62-9.375L160 301.3L54.63 406.6C48.38 412.9 40.19 416 32 416S15.63 412.9 9.375 406.6c-12.5-12.5-12.5-32.75 0-45.25l105.4-105.4L9.375 150.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L160 210.8l105.4-105.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-105.4 105.4L310.6 361.4z"/></svg>

  </span>


      </button>
    </header>
    <section class="flex-auto px-2 overflow-auto">
      <ul id="search-results">
        
      </ul>
    </section>
  </div>
</div>

  </div>
</body>

</html>
