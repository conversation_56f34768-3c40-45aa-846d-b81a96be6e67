if(typeof auth!="undefined"){var update_views,update_likes,viewsCollection=db.collection("views"),likesCollection=db.collection("likes");function numberWithCommas(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function toggleLoaders(e){var t,s,n=e.className;if(n=="")return;t=n.split(" ");for(s in t)e.classList.toggle(t[s])}update_views=function(e,t){viewsCollection.doc(t).onSnapshot(t=>{var n=t.data();n?e.innerText=numberWithCommas(n.views):e.innerText=0,toggleLoaders(e)})},update_likes=function(e,t){likesCollection.doc(t).onSnapshot(t=>{var n=t.data();n?e.innerText=numberWithCommas(n.likes):e.innerText=0,toggleLoaders(e)})},auth.signInAnonymously().then(()=>{var e,t,n,s,o=document.querySelectorAll("span[id^='views_']");for(n in o)e=o[n],t=e.id?e.id.replaceAll("/","-"):e.id,t&&update_views(e,t);s=document.querySelectorAll("span[id^='likes_']");for(n in s)e=s[n],t=e.id?e.id.replaceAll("/","-"):e.id,t&&update_likes(e,t)}).catch(e=>{var t=e.code,n=e.message;console.error(t,n)})}