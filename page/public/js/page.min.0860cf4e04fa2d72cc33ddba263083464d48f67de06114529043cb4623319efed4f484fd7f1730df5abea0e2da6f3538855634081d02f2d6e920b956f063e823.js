var viewed,liked,liked_page=!1,id=oid&&oid.replaceAll("/","-"),id_likes=oid_likes&&oid_likes.replaceAll("/","-");typeof auth!="undefined"&&(viewed=localStorage.getItem(id),viewed||auth.signInAnonymously().then(()=>{var e=db.collection("views").doc(id);localStorage.setItem(id,!0),e.get().then(e=>{e.exists?db.collection("views").doc(id).update({views:firebase.firestore.FieldValue.increment(1)}):db.collection("views").doc(id).set({views:1})}).catch(e=>{console.log("Error getting document:",e)})}).catch(e=>{var t=e.code,n=e.message;console.error(t,n)}),liked=localStorage.getItem(id_likes),liked&&(liked_page=!0,document.querySelectorAll("span[id='button_likes_heart']")[0].style.display="",document.querySelectorAll("span[id='button_likes_emtpty_heart']")[0].style.display="none",document.querySelectorAll("span[id='button_likes_text']")[0].innerText=""));function like_article(e){auth.signInAnonymously().then(()=>{var t=db.collection("likes").doc(e);t.get().then(t=>{liked_page=!0,localStorage.setItem(e,!0),document.querySelectorAll("span[id='button_likes_heart']")[0].style.display="",document.querySelectorAll("span[id='button_likes_emtpty_heart']")[0].style.display="none",document.querySelectorAll("span[id='button_likes_text']")[0].innerText="",t.exists?db.collection("likes").doc(e).update({likes:firebase.firestore.FieldValue.increment(1)}):db.collection("likes").doc(e).set({likes:1})}).catch(e=>{console.log("Error getting document:",e)})}).catch(e=>{var t=e.code,n=e.message;console.error(t,n)})}function remove_like_article(e){auth.signInAnonymously().then(()=>{var t=db.collection("likes").doc(e);t.get().then(t=>{liked_page=!1,localStorage.removeItem(e),document.querySelectorAll("span[id='button_likes_heart']")[0].style.display="none",document.querySelectorAll("span[id='button_likes_emtpty_heart']")[0].style.display="",document.querySelectorAll("span[id='button_likes_text']")[0].innerText="\xa0Like",t.exists?db.collection("likes").doc(e).update({likes:firebase.firestore.FieldValue.increment(-1)}):db.collection("likes").doc(e).set({likes:0})}).catch(e=>{console.log("Error getting document:",e)})}).catch(e=>{var t=e.code,n=e.message;console.error(t,n)})}function process_article(){liked_page?remove_like_article(id_likes):like_article(id_likes)}