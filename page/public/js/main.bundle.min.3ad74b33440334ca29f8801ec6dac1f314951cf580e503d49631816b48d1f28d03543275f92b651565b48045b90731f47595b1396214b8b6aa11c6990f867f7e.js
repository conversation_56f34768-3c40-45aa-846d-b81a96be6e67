e=this,t=function(){"use strict";function R(e,t){var n,s=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)),s}function h(e){for(var t,n=1;n<arguments.length;n++)t=null!=arguments[n]?arguments[n]:{},n%2?R(Object(t),!0).forEach(function(n){x(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):R(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))});return e}function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function F(e,t){for(var n,s=0;s<t.length;s++)n=t[s],n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,N(n.key),n)}function n(e,t,n){return t&&F(e.prototype,t),n&&F(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function x(e,t,n){return(t=N(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&v(e,t)}function g(e){return g=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},g(e)}function v(e,t){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},v(e,t)}function de(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function l(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}();return function(){var n,o,s=g(e);return t?(o=g(this).constructor,n=Reflect.construct(s,arguments,o)):n=s.apply(this,arguments),de(this,n)}}function d(e){return function(e){if(Array.isArray(e))return C(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?C(e,t):void 0}}(e)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function C(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,s=new Array(t);n<t;n++)s[n]=e[n];return s}function N(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n,s=e[Symbol.toPrimitive];if(void 0!==s){if(n=s.call(e,t||"default"),"object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function i(e){return Array.isArray?Array.isArray(e):"[object Array]"===P(e)}A=1/0;function oe(e){return e==null?"":function(e){if("string"==typeof e)return e;var t=e+"";return"0"==t&&1/e==-A?"-0":t}(e)}function o(e){return"string"==typeof e}function $(e){return"number"==typeof e}function J(e){return!0===e||!1===e||function(e){return B(e)&&null!==e}(e)&&"[object Boolean]"==P(e)}function B(e){return"object"===k(e)}function s(e){return e!=null}function O(e){return!e.trim().length}function P(e){return e==null?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}var ie=function(e){return"Missing ".concat(e," property in key")},ae=function(e){return"Property 'weight' in key '".concat(e,"' must be a positive integer")},z=Object.prototype.hasOwnProperty,pe=function(){function e(n){var s,o=this;t(this,e),this._keys=[],this._keyMap={},s=0,n.forEach(function(e){var t=H(e);o._keys.push(t),o._keyMap[t.id]=t,s+=t.weight}),this._keys.forEach(function(e){e.weight/=s})}return n(e,[{key:"get",value:function(e){return this._keyMap[e]}},{key:"keys",value:function(){return this._keys}},{key:"toJSON",value:function(){return JSON.stringify(this._keys)}}]),e}();function H(e){var t,n=null,s=null,a=null,r=1,c=null;if(o(e)||i(e))a=e,n=M(e),s=_(e);else{if(!z.call(e,"name"))throw new Error(ie("name"));if(t=e.name,a=t,z.call(e,"weight")&&(r=e.weight)<=0)throw new Error(ae(t));n=M(t),s=_(t),c=e.getFn}return{path:n,id:s,weight:r,src:a,getFn:c}}function M(e){return i(e)?e:e.split(".")}function _(e){return i(e)?e.join("."):e}var ue={useExtendedSearch:!1,getFn:function(e,t){var n=[],a=!1;return function e(t,r,c){if(s(t))if(r[c]){if(l=t[r[c]],!s(l))return;if(c===r.length-1&&(o(l)||$(l)||J(l)))n.push(oe(l));else if(i(l)){a=!0;for(var l,d=0,u=l.length;d<u;d+=1)e(l[d],r,c+1)}else r.length&&e(l,r,c+1)}else n.push(t)}(e,o(t)?t.split("."):t,0),a?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1},e=h(h(h(h({},{isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:function(e,t){return e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1}}),{includeMatches:!1,findAllMatches:!1,minMatchCharLength:1}),{location:0,threshold:.6,distance:100}),ue),ce=/[^ ]+/g,E=function(){function a(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=n.getFn,i=void 0===s?e.getFn:s,o=n.fieldNormWeight,r=void 0===o?e.fieldNormWeight:o;t(this,a),this.norm=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,e=new Map,t=Math.pow(10,s);return{get:function(s){if(o=s.match(ce).length,e.has(o))return e.get(o);var o,a=1/Math.pow(o,.5*n),i=parseFloat(Math.round(a*t)/t);return e.set(o,i),i},clear:function(){e.clear()}}}(r,3),this.getFn=i,this.isCreated=!1,this.setIndexRecords()}return n(a,[{key:"setSources",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.docs=e}},{key:"setIndexRecords",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.records=e}},{key:"setKeys",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.keys=e,this._keysMap={},e.forEach(function(e,n){t._keysMap[e.id]=n})}},{key:"create",value:function(){var e=this;!this.isCreated&&this.docs.length&&(this.isCreated=!0,o(this.docs[0])?this.docs.forEach(function(t,n){e._addString(t,n)}):this.docs.forEach(function(t,n){e._addObject(t,n)}),this.norm.clear())}},{key:"add",value:function(e){var t=this.size();o(e)?this._addString(e,t):this._addObject(e,t)}},{key:"removeAt",value:function(e){this.records.splice(e,1);for(var t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}},{key:"getValueForItemAtKeyId",value:function(e,t){return e[this._keysMap[t]]}},{key:"size",value:function(){return this.records.length}},{key:"_addString",value:function(e,t){if(s(e)&&!O(e)){var n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}}},{key:"_addObject",value:function(e,t){var n=this,a={i:t,$:{}};this.keys.forEach(function(t,r){if(c=t.getFn?t.getFn(e):n.getFn(e,t.path),s(c))if(i(c)){for(u=[],d=[{nestedArrIndex:-1,value:c}];d.length;){var c,d,u,m,f,h=d.pop(),p=h.nestedArrIndex,l=h.value;s(l)&&(o(l)&&!O(l)?(m={v:l,i:p,n:n.norm.get(l)},u.push(m)):i(l)&&l.forEach(function(e,t){d.push({nestedArrIndex:t,value:e})}))}a.$[r]=u}else o(c)&&!O(c)&&(f={v:c,n:n.norm.get(c)},a.$[r]=f)}),this.records.push(a)}},{key:"toJSON",value:function(){return{keys:this.keys,records:this.records}}}]),a}();function L(t,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=o.getFn,r=void 0===i?e.getFn:i,a=o.fieldNormWeight,c=void 0===a?e.fieldNormWeight:a,s=new E({getFn:r,fieldNormWeight:c});return s.setKeys(t.map(H)),s.setSources(n),s.create(),s}function p(t){var s,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=n.errors,f=void 0===d?0:d,l=n.currentLocation,u=void 0===l?0:l,a=n.expectedLocation,h=void 0===a?0:a,r=n.distance,c=void 0===r?e.distance:r,i=n.ignoreLocation,m=void 0===i?e.ignoreLocation:i,o=f/t.length;return m?o:(s=Math.abs(h-u),c?o+s/c:s?1:o)}a=32;function K(t,n,s){var o,r,f,v,b,w,k,A,H,c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},F=c.location,$=void 0===F?e.location:F,R=c.distance,C=void 0===R?e.distance:R,N=c.threshold,V=void 0===N?e.threshold:N,I=c.findAllMatches,W=void 0===I?e.findAllMatches:I,P=c.minMatchCharLength,z=void 0===P?e.minMatchCharLength:P,B=c.includeMatches,D=void 0===B?e.includeMatches:B,L=c.ignoreLocation,y=void 0===L?e.ignoreLocation:L;if(n.length>a)throw new Error("Pattern length exceeds max of ".concat(a,"."));for(var _,d=n.length,g=t.length,i=Math.max(0,Math.min($,g)),m=V,h=i,x=z>1||D,S=x?Array(g):[];(_=t.indexOf(n,h))>-1;)if(H=p(n,{currentLocation:_,expectedLocation:i,distance:C,ignoreLocation:y}),m=Math.min(H,m),h=_+d,x)for(b=0;b<d;)S[_+b]=1,b+=1;for(var h=-1,O=[],E=1,j=d+g,U=1<<d-1,l=0;l<d;l+=1){for(f=0,r=j;f<r;)p(n,{errors:l,currentLocation:i+r,expectedLocation:i,distance:C,ignoreLocation:y})<=m?f=r:j=r,r=Math.floor((j-f)/2+f);var j=r,T=Math.max(1,i-r+1),M=W?g:Math.min(i+r,g)+d,u=Array(M+2);u[M+1]=(1<<l)-1;for(o=M;o>=T;o-=1)if(v=o-1,A=s[t.charAt(v)],x&&(S[v]=+!!A),u[o]=(u[o+1]<<1|1)&A,l&&(u[o]|=(O[o+1]|O[o])<<1|1|O[o+1]),u[o]&U&&(E=p(n,{errors:l,currentLocation:v,expectedLocation:i,distance:C,ignoreLocation:y}))<=m){if(m=E,(h=v)<=i)break;T=Math.max(1,2*i-h)}if(p(n,{errors:l+1,currentLocation:i,expectedLocation:i,distance:C,ignoreLocation:y})>m)break;O=u}return w={isMatch:h>=0,score:Math.max(.001,E)},x&&(k=function(){for(var i,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.minMatchCharLength,o=[],t=-1,r=-1,n=0,c=s.length;n<c;n+=1)i=s[n],i&&-1===t?t=n:i||-1===t||((r=n-1)-t+1>=a&&o.push([t,r]),t=-1);return s[n-1]&&n-t>=a&&o.push([t,n-1]),o}(S,z),k.length?D&&(w.indices=k):w.isMatch=!1),w}function ee(e){for(var s,n={},t=0,o=e.length;t<o;t+=1)s=e.charAt(t),n[s]=(n[s]||0)|1<<o-t-1;return n}var m=String.prototype.normalize?function(e){return e.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,"")}:function(e){return e},V=function(){function s(n){var i,r,l,x=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},h=o.location,A=void 0===h?e.location:h,u=o.threshold,E=void 0===u?e.threshold:u,v=o.distance,F=void 0===v?e.distance:v,f=o.includeMatches,S=void 0===f?e.includeMatches:f,g=o.findAllMatches,k=void 0===g?e.findAllMatches:g,d=o.minMatchCharLength,O=void 0===d?e.minMatchCharLength:d,y=o.isCaseSensitive,_=void 0===y?e.isCaseSensitive:y,w=o.ignoreDiacritics,j=void 0===w?e.ignoreDiacritics:w,b=o.ignoreLocation,C=void 0===b?e.ignoreLocation:b;if(t(this,s),this.options={location:A,threshold:E,distance:F,includeMatches:S,findAllMatches:k,minMatchCharLength:O,isCaseSensitive:_,ignoreDiacritics:j,ignoreLocation:C},n=_?n:n.toLowerCase(),n=j?m(n):n,this.pattern=n,this.chunks=[],this.pattern.length)if(r=function(e,t){x.chunks.push({pattern:e,alphabet:ee(e),startIndex:t})},i=this.pattern.length,i>a){for(var c=0,p=i%a,M=i-p;c<M;)r(this.pattern.substr(c,a),c),c+=a;p&&(l=i-a,r(this.pattern.substr(l),l))}else r(this.pattern,0)}return n(s,[{key:"searchIn",value:function(e){var o=this.options,v=o.isCaseSensitive,b=o.ignoreDiacritics,r=o.includeMatches;if(e=v?e:e.toLowerCase(),e=b?m(e):e,this.pattern===e)return i={isMatch:!0,score:0},r&&(i.indices=[[0,e.length-1]]),i;var i,a,t=this.options,l=t.location,g=t.distance,p=t.threshold,u=t.findAllMatches,h=t.minMatchCharLength,f=t.ignoreLocation,s=[],c=0,n=!1;return this.chunks.forEach(function(t){var m=t.pattern,v=t.alphabet,b=t.startIndex,o=K(e,m,v,{location:l+b,distance:g,threshold:p,findAllMatches:u,minMatchCharLength:h,includeMatches:r,ignoreLocation:f}),i=o.isMatch,j=o.score,a=o.indices;i&&(n=!0),c+=j,i&&a&&(s=[].concat(d(s),d(a)))}),a={isMatch:n,score:n?c/this.chunks.length:1},n&&r&&(a.indices=s),a}}]),s}(),c=function(){function e(n){t(this,e),this.pattern=n}return n(e,[{key:"search",value:function(){}}],[{key:"isMultiMatch",value:function(e){return W(e,this.multiRegex)}},{key:"isSingleMatch",value:function(e){return W(e,this.singleRegex)}}]),e}();function W(e,t){var n=e.match(t);return n?n[1]:null}var a,u,A,Z=function(e){r(s,e);var o=l(s);function s(e){return t(this,s),o.call(this,e)}return n(s,[{key:"search",value:function(e){var t=e===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}}],[{key:"type",get:function(){return"exact"}},{key:"multiRegex",get:function(){return/^="(.*)"$/}},{key:"singleRegex",get:function(){return/^=(.*)$/}}]),s}(c),Q=function(e){r(s,e);var o=l(s);function s(e){return t(this,s),o.call(this,e)}return n(s,[{key:"search",value:function(e){var t=-1===e.indexOf(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}}],[{key:"type",get:function(){return"inverse-exact"}},{key:"multiRegex",get:function(){return/^!"(.*)"$/}},{key:"singleRegex",get:function(){return/^!(.*)$/}}]),s}(c),se=function(e){r(s,e);var o=l(s);function s(e){return t(this,s),o.call(this,e)}return n(s,[{key:"search",value:function(e){var t=e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}}],[{key:"type",get:function(){return"prefix-exact"}},{key:"multiRegex",get:function(){return/^\^"(.*)"$/}},{key:"singleRegex",get:function(){return/^\^(.*)$/}}]),s}(c),Y=function(e){r(s,e);var o=l(s);function s(e){return t(this,s),o.call(this,e)}return n(s,[{key:"search",value:function(e){var t=!e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}}],[{key:"type",get:function(){return"inverse-prefix-exact"}},{key:"multiRegex",get:function(){return/^!\^"(.*)"$/}},{key:"singleRegex",get:function(){return/^!\^(.*)$/}}]),s}(c),G=function(e){r(s,e);var o=l(s);function s(e){return t(this,s),o.call(this,e)}return n(s,[{key:"search",value:function(e){var t=e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[e.length-this.pattern.length,e.length-1]}}}],[{key:"type",get:function(){return"suffix-exact"}},{key:"multiRegex",get:function(){return/^"(.*)"\$$/}},{key:"singleRegex",get:function(){return/^(.*)\$$/}}]),s}(c),X=function(e){r(s,e);var o=l(s);function s(e){return t(this,s),o.call(this,e)}return n(s,[{key:"search",value:function(e){var t=!e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}}],[{key:"type",get:function(){return"inverse-suffix-exact"}},{key:"multiRegex",get:function(){return/^!"(.*)"\$$/}},{key:"singleRegex",get:function(){return/^!(.*)\$$/}}]),s}(c),S=function(s){r(o,s);var i=l(o);function o(n){var d,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=s.location,O=void 0===r?e.location:r,l=s.threshold,g=void 0===l?e.threshold:l,u=s.distance,_=void 0===u?e.distance:u,m=s.includeMatches,j=void 0===m?e.includeMatches:m,p=s.findAllMatches,v=void 0===p?e.findAllMatches:p,a=s.minMatchCharLength,b=void 0===a?e.minMatchCharLength:a,f=s.isCaseSensitive,y=void 0===f?e.isCaseSensitive:f,h=s.ignoreDiacritics,w=void 0===h?e.ignoreDiacritics:h,c=s.ignoreLocation,x=void 0===c?e.ignoreLocation:c;return t(this,o),(d=i.call(this,n))._bitapSearch=new V(n,{location:O,threshold:g,distance:_,includeMatches:j,findAllMatches:v,minMatchCharLength:b,isCaseSensitive:y,ignoreDiacritics:w,ignoreLocation:x}),d}return n(o,[{key:"search",value:function(e){return this._bitapSearch.searchIn(e)}}],[{key:"type",get:function(){return"fuzzy"}},{key:"multiRegex",get:function(){return/^"(.*)"$/}},{key:"singleRegex",get:function(){return/^(.*)$/}}]),o}(c),U=function(e){r(s,e);var o=l(s);function s(e){return t(this,s),o.call(this,e)}return n(s,[{key:"search",value:function(e){for(var t,o,n=0,s=[],i=this.pattern.length;(t=e.indexOf(this.pattern,n))>-1;)n=t+i,s.push([t,n-1]);return o=!!s.length,{isMatch:o,score:o?0:1,indices:s}}}],[{key:"type",get:function(){return"include"}},{key:"multiRegex",get:function(){return/^'"(.*)"$/}},{key:"singleRegex",get:function(){return/^'(.*)$/}}]),s}(c),j=[Z,U,se,Y,X,G,Q,S],I=j.length,te=/ +(?=(?:[^"]*"[^"]*")*[^"]*$)/,ne=new Set([S.type,U.type]),q=function(){function s(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=o.isCaseSensitive,r=void 0===a?e.isCaseSensitive:a,c=o.ignoreDiacritics,l=void 0===c?e.ignoreDiacritics:c,d=o.includeMatches,O=void 0===d?e.includeMatches:d,h=o.minMatchCharLength,v=void 0===h?e.minMatchCharLength:h,f=o.ignoreLocation,_=void 0===f?e.ignoreLocation:f,g=o.findAllMatches,b=void 0===g?e.findAllMatches:g,i=o.location,y=void 0===i?e.location:i,p=o.threshold,w=void 0===p?e.threshold:p,u=o.distance,x=void 0===u?e.distance:u;t(this,s),this.query=null,this.options={isCaseSensitive:r,ignoreDiacritics:l,includeMatches:O,minMatchCharLength:v,findAllMatches:b,ignoreLocation:_,location:y,threshold:w,distance:x},n=r?n:n.toLowerCase(),n=l?m(n):n,this.pattern=n,this.query=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.split("|").map(function(e){for(var a,r,c,l,d=e.trim().split(te).filter(function(e){return e&&!!e.trim()}),s=[],o=0,h=d.length;o<h;o+=1){for(var u=d[o],i=!1,n=-1;!i&&++n<I;)a=j[n],r=a.isMultiMatch(u),r&&(s.push(new a(r,t)),i=!0);if(!i)for(n=-1;++n<I;)if(c=j[n],l=c.isSingleMatch(u),l){s.push(new c(l,t));break}}return s})}(this.pattern,this.options)}return n(s,[{key:"searchIn",value:function(e){if(o=this.query,!o)return{isMatch:!1,score:1};var a=this.options,h=a.includeMatches,w=a.isCaseSensitive,b=a.ignoreDiacritics;e=w?e:e.toLowerCase(),e=b?m(e):e;for(var s,o,i,r,f,v,n=0,t=[],c=0,l=0,j=o.length;l<j;l+=1){i=o[l],t.length=0;for(n=0,s=0,f=i.length;s<f;s+=1){var p=i[s],u=p.search(e),y=u.isMatch,g=u.indices,_=u.score;if(!y){c=0,n=0,t.length=0;break}n+=1,c+=_,h&&(v=p.constructor.type,ne.has(v)?t=[].concat(d(t),d(g)):t.push(g))}if(n)return r={isMatch:!0,score:c/n},h&&(r.indices=t),r}return{isMatch:!1,score:1}}}],[{key:"condition",value:function(e,t){return t.useExtendedSearch}}]),s}(),f=[];function w(e,t){for(var s,n=0,o=f.length;n<o;n+=1)if(s=f[n],s.condition(e,t))return new s(e,t);return new V(e,t)}var y="$and",re="$or",D="$path",le="$val",b=function(e){return!!e[y]||!!e[re]},T=function(e){return x({},y,Object.keys(e).map(function(t){return x({},t,e[t])}))};function he(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).auto,s=void 0===n||n;return b(e)||(e=T(e)),function e(n){var r,c,d,u,a=Object.keys(n),l=function(e){return!!e[D]}(n);if(!l&&a.length>1&&!b(n))return e(T(n));if(function(e){return!i(e)&&B(e)&&!b(e)}(n)){if(r=l?n[D]:a[0],c=l?n[le]:n[r],!o(c))throw new Error(function(e){return"Invalid value for key ".concat(e)}(r));return d={keyId:_(r),pattern:c},s&&(d.searcher=w(c,t)),d}return u={children:[],operator:a[0]},a.forEach(function(t){var s=n[t];i(s)&&s.forEach(function(t){u.children.push(e(t))})}),u}(e)}function me(e,t){var n=e.matches;t.matches=[],s(n)&&n.forEach(function(e){if(s(e.indices)&&e.indices.length){var n={indices:e.indices,value:e.value};e.key&&(n.key=e.key.src),e.idx>-1&&(n.refIndex=e.idx),t.matches.push(n)}})}function fe(e,t){t.score=e.score}return u=function(){function a(n){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0;t(this,a),this.options=h(h({},e),s),this.options.useExtendedSearch,this._keyStore=new pe(this.options.keys),this.setCollection(n,o)}return n(a,[{key:"setCollection",value:function(e,t){if(this._docs=e,t&&!(t instanceof E))throw new Error("Incorrect 'index' type");this._myIndex=t||L(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}},{key:"add",value:function(e){s(e)&&(this._docs.push(e),this._myIndex.add(e))}},{key:"remove",value:function(){for(var t,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!1},n=[],e=0,s=this._docs.length;e<s;e+=1)t=this._docs[e],o(t,e)&&(this.removeAt(e),e-=1,s-=1,n.push(t));return n}},{key:"removeAt",value:function(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}},{key:"getIndex",value:function(){return this._myIndex}},{key:"search",value:function(t){var a=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).limit,i=void 0===a?-1:a,n=this.options,r=n.includeMatches,c=n.includeScore,l=n.shouldSort,d=n.sortFn,u=n.ignoreFieldNorm,s=o(t)?o(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(t,n){var s=n.ignoreFieldNorm,o=void 0===s?e.ignoreFieldNorm:s;t.forEach(function(e){var t=1;e.matches.forEach(function(e){var n=e.key,a=e.norm,s=e.score,i=n?n.weight:null;t*=Math.pow(0===s&&i?Number.EPSILON:s,(i||1)*(o?1:a))}),e.score=t})}(s,{ignoreFieldNorm:u}),l&&s.sort(d),$(i)&&i>-1&&(s=s.slice(0,i)),function(t,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=o.includeMatches,r=void 0===i?e.includeMatches:i,a=o.includeScore,c=void 0===a?e.includeScore:a,s=[];return r&&s.push(me),c&&s.push(fe),t.map(function(e){var t=e.idx,o={item:n[t],refIndex:t};return s.length&&s.forEach(function(t){t(e,o)}),o})}(s,this._docs,{includeMatches:r,includeScore:c})}},{key:"_searchStringList",value:function(e){var n=w(e,this.options),o=this._myIndex.records,t=[];return o.forEach(function(e){var o=e.v,a=e.i,r=e.n;if(s(o)){var i=n.searchIn(o),c=i.isMatch,l=i.score,d=i.indices;c&&t.push({item:o,idx:a,matches:[{score:l,value:o,norm:r,indices:d}]})}}),t}},{key:"_searchLogical",value:function(e){var n=this,i=he(e,this.options),a=function e(t,s,o){if(!t.children){var c,l=t.keyId,u=t.searcher,i=n._findMatches({key:n._keyStore.get(l),value:n._myIndex.getValueForItemAtKeyId(s,l),searcher:u});return i&&i.length?[{idx:o,item:s,matches:i}]:[]}for(var a=[],r=0,h=t.children.length;r<h;r+=1)if(c=e(t.children[r],s,o),c.length)a.push.apply(a,d(c));else if(t.operator===y)return[];return a},r=this._myIndex.records,t={},o=[];return r.forEach(function(e){var c,r=e.$,n=e.i;s(r)&&(c=a(i,r,n),c.length&&(t[n]||(t[n]={idx:n,item:r,matches:[]},o.push(t[n])),c.forEach(function(e){var s,o=e.matches;(s=t[n].matches).push.apply(s,d(o))})))}),o}},{key:"_searchObjectList",value:function(e){var o=this,i=w(e,this.options),t=this._myIndex,a=t.keys,r=t.records,n=[];return r.forEach(function(e){var t,r=e.$,c=e.i;s(r)&&(t=[],a.forEach(function(e,n){t.push.apply(t,d(o._findMatches({key:e,value:r[n],searcher:i})))}),t.length&&n.push({idx:c,item:r,matches:t}))}),n}},{key:"_findMatches",value:function(e){var a=e.key,t=e.value,r=e.searcher;if(!s(t))return[];if(n=[],i(t))t.forEach(function(e){var t=e.v,i=e.i,c=e.n;if(s(t)){var o=r.searchIn(t),l=o.isMatch,d=o.score,u=o.indices;l&&n.push({score:d,key:a,value:t,idx:i,norm:c,indices:u})}});else{var n,c=t.v,l=t.n,o=r.searchIn(c),d=o.isMatch,u=o.score,h=o.indices;d&&n.push({score:u,key:a,value:c,norm:l,indices:h})}return n}}]),a}(),u.version="7.1.0",u.createIndex=L,u.parseIndex=function(t){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=s.getFn,a=void 0===o?e.getFn:o,i=s.fieldNormWeight,r=void 0===i?e.fieldNormWeight:i,c=t.keys,l=t.records,n=new E({getFn:a,fieldNormWeight:r});return n.setKeys(c),n.setIndexRecords(l),n},u.config=e,function(){f.push.apply(f,arguments)}(q),u},"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Fuse=t();var e,t,fuse,scriptBundle,copyText,copiedText,menuButton,menuCloseButton,menuWrapper,menuOpen,openMenu,closeMenu,showButton=document.getElementById("search-button"),showButtonMobile=document.getElementById("search-button-mobile"),hideButton=document.getElementById("close-search-button"),wrapper=document.getElementById("search-wrapper"),modal=document.getElementById("search-modal"),input=document.getElementById("search-query"),output=document.getElementById("search-results"),first=output.firstChild,last=output.lastChild,searchVisible=!1,indexed=!1,hasResults=!1;showButton?showButton.addEventListener("click",displaySearch):null,showButtonMobile?showButtonMobile.addEventListener("click",displaySearch):null,hideButton.addEventListener("click",hideSearch),wrapper.addEventListener("click",hideSearch),modal.addEventListener("click",function(e){return e.stopPropagation(),e.stopImmediatePropagation(),!1}),document.addEventListener("keydown",function(e){e.key=="/"&&(searchVisible||(e.preventDefault(),displaySearch())),e.key=="Escape"&&hideSearch(),e.key=="ArrowDown"&&searchVisible&&hasResults&&(e.preventDefault(),document.activeElement==input?first.focus():document.activeElement==last?last.focus():document.activeElement.parentElement.nextSibling.firstElementChild.focus()),e.key=="ArrowUp"&&searchVisible&&hasResults&&(e.preventDefault(),document.activeElement==input?input.focus():document.activeElement==first?input.focus():document.activeElement.parentElement.previousSibling.firstElementChild.focus()),e.key=="Enter"&&searchVisible&&hasResults&&(e.preventDefault(),document.activeElement==input?first.focus():document.activeElement.click())}),input.onkeyup=function(){executeQuery(this.value)};function displaySearch(){indexed||buildIndex(),searchVisible||(document.body.style.overflow="hidden",wrapper.style.visibility="visible",input.focus(),searchVisible=!0)}function hideSearch(){searchVisible&&(document.body.style.overflow="visible",wrapper.style.visibility="hidden",input.value="",output.innerHTML="",document.activeElement.blur(),searchVisible=!1)}function fetchJSON(e,t){var n=new XMLHttpRequest;n.onreadystatechange=function(){if(n.readyState===4&&n.status===200){var e=JSON.parse(n.responseText);t&&t(e)}},n.open("GET",e),n.send()}function buildIndex(){var e=wrapper.getAttribute("data-url"),e=e.replace(/\/?$/,"/");fetchJSON(e+"index.json",function(e){var t={shouldSort:!0,ignoreLocation:!0,threshold:0,includeMatches:!0,keys:[{name:"title",weight:.8},{name:"section",weight:.2},{name:"summary",weight:.6},{name:"content",weight:.4}]};fuse=new Fuse(e,t),indexed=!0})}function executeQuery(e){let n=fuse.search(e),t="";n.length>0?(n.forEach(function(e){console.log(e.item.summary);var o,i,a=e.item.summary,s=document.createElement("div");s.innerHTML=a,e.item.summary=s.textContent||s.innerText||"",o=e.item.externalUrl?e.item.title+'<span class="text-xs ml-2 align-center cursor-default text-neutral-400 dark:text-neutral-500">'+e.item.externalUrl+"</span>":e.item.title,i=e.item.externalUrl?'target="_blank" rel="noopener" href="'+e.item.externalUrl+'"':'href="'+e.item.permalink+'"',t=t+`<li class="mb-2">
          <a class="flex items-center px-3 py-2 rounded-md appearance-none bg-neutral-100 dark:bg-neutral-700 focus:bg-primary-100 hover:bg-primary-100 dark:hover:bg-primary-900 dark:focus:bg-primary-900 focus:outline-dotted focus:outline-transparent focus:outline-2" 
          ${i} tabindex="0">
            <div class="grow">
              <div class="-mb-1 text-lg font-bold">
                ${o}
              </div>
              <div class="text-sm text-neutral-500 dark:text-neutral-400">${e.item.section}<span class="px-2 text-primary-500">&middot;</span>${e.item.date?e.item.date:""}</span></div>
              <div class="text-sm italic">${e.item.summary}</div>
            </div>
            <div class="ml-2 ltr:block rtl:hidden text-neutral-500">&rarr;</div>
            <div class="mr-2 ltr:hidden rtl:block text-neutral-500">&larr;</div>
          </a>
        </li>`}),hasResults=!0):(t="",hasResults=!1),output.innerHTML=t,n.length>0&&(first=output.firstChild.firstElementChild,last=output.lastChild.firstElementChild)}scriptBundle=document.getElementById("script-bundle"),copyText=scriptBundle&&scriptBundle.getAttribute("data-copy")?scriptBundle.getAttribute("data-copy"):"Copy",copiedText=scriptBundle&&scriptBundle.getAttribute("data-copied")?scriptBundle.getAttribute("data-copied"):"Copied";function createCopyButton(e){const t=document.createElement("button");t.className="copy-button",t.type="button",t.ariaLabel=copyText,t.innerText=copyText,t.addEventListener("click",()=>copyCodeToClipboard(t,e)),addCopyButtonToDom(t,e)}async function copyCodeToClipboard(e,t){const n=t.querySelector(":last-child").innerText;try{result=await navigator.permissions.query({name:"clipboard-write"}),result.state=="granted"||result.state=="prompt"?await navigator.clipboard.writeText(n):copyCodeBlockExecCommand(n,t)}catch{copyCodeBlockExecCommand(n,t)}finally{codeWasCopied(e)}}function copyCodeBlockExecCommand(e,t){const n=document.createElement("textArea");n.contentEditable="true",n.readOnly="false",n.className="copy-textarea",n.value=e,t.insertBefore(n,t.firstChild);const s=document.createRange();s.selectNodeContents(n);const o=window.getSelection();o.removeAllRanges(),o.addRange(s),n.setSelectionRange(0,999999),document.execCommand("copy"),t.removeChild(n)}function codeWasCopied(e){e.blur(),e.innerText=copiedText,setTimeout(function(){e.innerText=copyText},2e3)}function addCopyButtonToDom(e,t){t.insertBefore(e,t.firstChild);const n=document.createElement("div");n.className="highlight-wrapper",t.parentNode.insertBefore(n,t),n.appendChild(t)}window.addEventListener("DOMContentLoaded",e=>{document.querySelectorAll(".highlight").forEach(e=>createCopyButton(e))}),menuButton=document.getElementById("menu-button"),menuCloseButton=document.getElementById("menu-close-button"),menuWrapper=document.getElementById("menu-wrapper"),menuOpen=!1,openMenu=function(){menuOpen||(menuOpen=!0,document.body.style.overflowY="hidden",menuButton.style.visibility="hidden",menuWrapper.style.visibility="visible",menuWrapper.style.opacity="1",window.onbeforeunload=function(){closeMenu()})},closeMenu=function(e){menuOpen&&(menuOpen=!1,document.body.style.overflowY="auto",menuButton.style.visibility="visible",menuWrapper.style.visibility="hidden",menuWrapper.style.opacity="0",window.onbeforeunload=function(){},e.stopPropagation())},menuButton.addEventListener("click",openMenu),menuCloseButton.addEventListener("click",closeMenu)