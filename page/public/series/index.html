<!DOCTYPE html>
<html lang="pl" dir="ltr" class="scroll-smooth" data-default-appearance="dark"
  data-auto-appearance="false"><head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="utf-8" />
  
  <meta http-equiv="content-language" content="pl" />
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  
  <title>Series &middot; Python Łódź</title>
  <meta name="title" content="Series &middot; Python Łódź" />
  
  
  
  
  
  <link rel="canonical" href="http://localhost:1313/series/" />
  
  <link rel="alternate" type="application/rss+xml" href="/series/index.xml" title="Python Łódź" />
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <link type="text/css" rel="stylesheet" href="/css/main.bundle.min.34d7373bbf62125b4a19bef366fe4fdef50fae18b4c53e765a59480da810b01e886160b5ff317f1ae96cef62f058c77940a42f4e588c0ab95e549205d24caab0.css"
    integrity="sha512-NNc3O79iEltKGb7zZv5P3vUPrhi0xT52WllIDagQsB6IYWC1/zF/Guls72LwWMd5QKQvTliMCrleVJIF0kyqsA==" />
  
  
  <script type="text/javascript" src="/js/appearance.min.516a16745bea5a9bd011138d254cc0fd3973cd55ce6e15f3dec763e7c7c2c7448f8fe7b54cca811cb821b0c7e12cd161caace1dd794ac3d34d40937cbcc9ee12.js"
    integrity="sha512-UWoWdFvqWpvQERONJUzA/TlzzVXObhXz3sdj58fCx0SPj&#43;e1TMqBHLghsMfhLNFhyqzh3XlKw9NNQJN8vMnuEg=="></script>
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  <script defer type="text/javascript" id="script-bundle" src="/js/main.bundle.min.3ad74b33440334ca29f8801ec6dac1f314951cf580e503d49631816b48d1f28d03543275f92b651565b48045b90731f47595b1396214b8b6aa11c6990f867f7e.js"
    integrity="sha512-OtdLM0QDNMop&#43;IAextrB8xSVHPWA5QPUljGBa0jR8o0DVDJ1&#43;StlFWW0gEW5BzH0dZWxOWIUuLaqEcaZD4Z/fg==" data-copy="" data-copied=""></script>
  
  
  
  <script src="/lib/zoom/zoom.min.37d2094687372da3f7343a221a470f6b8806f7891aa46a5a03966af7f0ebd38b9fe536cb154e6ad28f006d184b294525a7c4054b6bbb4be62d8b453b42db99bd.js" integrity="sha512-N9IJRoc3LaP3NDoiGkcPa4gG94kapGpaA5Zq9/Dr04uf5TbLFU5q0o8AbRhLKUUlp8QFS2u7S&#43;Yti0U7QtuZvQ=="></script>
  
  
  
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
  <link rel="manifest" href="/site.webmanifest" />
  
  
  
  
  
  
  
  
  <meta property="og:url" content="http://localhost:1313/series/">
  <meta property="og:site_name" content="Python Łódź">
  <meta property="og:title" content="Series">
  <meta property="og:locale" content="pl">
  <meta property="og:type" content="website">

  
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="Series">

  
  

  
  
  <meta name="author" content="Python Łódź" />
  
  
  
  <link href="https://discord.gg/jbvWBMufEf" rel="me" />
  
  
  <link href="https://www.facebook.com/pythonlodz" rel="me" />
  
  
  <link href="https://github.com/python-lodz" rel="me" />
  
  
  <link href="https://www.linkedin.com/company/python-lodz" rel="me" />
  
  
  <link href="https://www.youtube.com/@pythonlodz" rel="me" />
  
  
  
  

<script src="/lib/jquery/jquery.slim.min.b0dca576e87d7eaa5850ae4e61759c065786cdb6489d68fcc82240539eebd5da522bdb4fda085ffd245808c8fe2acb2516408eb774ef26b5f6015fc6737c0ea8.js" integrity="sha512-sNylduh9fqpYUK5OYXWcBleGzbZInWj8yCJAU57r1dpSK9tP2ghf/SRYCMj&#43;KsslFkCOt3TvJrX2AV/Gc3wOqA=="></script>






















  
  

<script async src="https://www.googletagmanager.com/gtag/js?id=G-SL732ZDGYL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-SL732ZDGYL');
</script>



  
  
  



<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '1849865132461926');
fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none"
src="https://www.facebook.com/tr?id=1849865132461926&ev=PageView&noscript=1"
/></noscript>



  
  <meta name="theme-color"/>
  
  
</head>
<body
  class="flex flex-col h-screen px-6 m-auto text-lg leading-7 max-w-7xl bg-neutral text-neutral-900 dark:bg-neutral-800 dark:text-neutral sm:px-14 md:px-24 lg:px-32 scrollbar-thin scrollbar-track-neutral-200 scrollbar-thumb-neutral-400 dark:scrollbar-track-neutral-800 dark:scrollbar-thumb-neutral-600">
  <div id="the-top" class="absolute flex self-center">
    <a class="px-3 py-1 text-sm -translate-y-8 rounded-b-lg bg-primary-200 focus:translate-y-0 dark:bg-neutral-600"
      href="#main-content"><span
        class="font-bold text-primary-600 ltr:pr-2 rtl:pl-2 dark:text-primary-400">&darr;</span>Przewiń do głównej treści</a>
  </div>
  
  
  <div style="padding-left:0;padding-right:0;padding-top:2px;padding-bottom:3px"
    class="main-menu flex items-center justify-between px-4 py-6 sm:px-6 md:justify-start gap-x-3">
    
    <div class="flex flex-1 items-center justify-between">
        <nav class="flex space-x-3">

            
            <a href="/" class="text-base font-medium text-gray-500 hover:text-gray-900">Python Łódź</a>
            

        </nav>
        <nav class="hidden md:flex items-center gap-x-5 md:ml-12 h-12">

            
            
            
  <a href="/sponsorzy/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
    
    <p class="text-base font-medium" title="Sponsorzy">
        Sponsorzy
    </p>
</a>



            
            
  <a href="/spotkania/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
    
    <p class="text-base font-medium" title="Spotkania">
        Spotkania
    </p>
</a>



            
            

            


            
            <button id="search-button" aria-label="Search" class="text-base hover:text-primary-600 dark:hover:text-primary-400"
                title="">
                

  <span class="relative block icon">
    <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" class="svg-inline--fa fa-search fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"/></svg>

  </span>


            </button>
            


            
            

        </nav>
        <div class="flex md:hidden items-center gap-x-5 md:ml-12 h-12">

            <span></span>

            


            
            <button id="search-button-mobile" aria-label="Search" class="text-base hover:text-primary-600 dark:hover:text-primary-400"
                title="">
                

  <span class="relative block icon">
    <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" class="svg-inline--fa fa-search fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"/></svg>

  </span>


            </button>
            

            
            

        </div>
    </div>
    <div class="-my-2 md:hidden">

        <label id="menu-button" class="block">
            
            <div class="cursor-pointer hover:text-primary-600 dark:hover:text-primary-400">
                

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M0 96C0 78.33 14.33 64 32 64H416C433.7 64 448 78.33 448 96C448 113.7 433.7 128 416 128H32C14.33 128 0 113.7 0 96zM0 256C0 238.3 14.33 224 32 224H416C433.7 224 448 238.3 448 256C448 273.7 433.7 288 416 288H32C14.33 288 0 273.7 0 256zM416 448H32C14.33 448 0 433.7 0 416C0 398.3 14.33 384 32 384H416C433.7 384 448 398.3 448 416C448 433.7 433.7 448 416 448z"/></svg>

  </span>


            </div>
            <div id="menu-wrapper" style="padding-top:5px;"
                class="fixed inset-0 z-30 invisible w-screen h-screen m-0 overflow-auto transition-opacity opacity-0 cursor-default bg-neutral-100/50 backdrop-blur-sm dark:bg-neutral-900/50">
                <ul
                    class="flex space-y-2 mt-3 flex-col items-end w-full px-6 py-6 mx-auto overflow-visible list-none ltr:text-right rtl:text-left max-w-7xl">

                    <li id="menu-close-button">
                        <span
                            class="cursor-pointer inline-block align-text-bottom hover:text-primary-600 dark:hover:text-primary-400">

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M310.6 361.4c12.5 12.5 12.5 32.75 0 45.25C304.4 412.9 296.2 416 288 416s-16.38-3.125-22.62-9.375L160 301.3L54.63 406.6C48.38 412.9 40.19 416 32 416S15.63 412.9 9.375 406.6c-12.5-12.5-12.5-32.75 0-45.25l105.4-105.4L9.375 150.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L160 210.8l105.4-105.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-105.4 105.4L310.6 361.4z"/></svg>

  </span>

</span>
                    </li>

                    

                    
  <li class="mt-1">
    <a href="/sponsorzy/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
        
        <p class="text-bg font-bg" title="Sponsorzy">
            Sponsorzy
        </p>
    </a>
</li>




                    

                    
  <li class="mt-1">
    <a href="/spotkania/"  class="flex items-center text-gray-500 hover:text-primary-600 dark:hover:text-primary-400">
        
        <p class="text-bg font-bg" title="Spotkania">
            Spotkania
        </p>
    </a>
</li>




                    

                </ul>
                
                

            </div>
        </label>
    </div>
</div>




<script>
    (function () {
        var $mainmenu = $('.main-menu');
        var path = window.location.pathname;
        $mainmenu.find('a[href="' + path + '"]').each(function (i, e) {
            $(e).children('p').addClass('active');
        });
    })();
</script>


  
  <div class="relative flex flex-col grow">
    <main id="main-content" class="grow">
      






 

 

  <header>
    
    <h1 class="mt-5 text-4xl font-extrabold text-neutral-900 dark:text-neutral">Series</h1>
    <div class="mt-1 mb-2 text-base text-neutral-500 dark:text-neutral-400 print:hidden">
      





















<div class="flex flex-row flex-wrap items-center">
  
  
</div>


    </div>
  </header>
  

  

  <section class="flex flex-wrap max-w-prose -mx-2 overflow-hidden">
    
  </section>

  



      
    </main><footer id="site-footer" class="py-10 print:hidden">
  
  
    
    <nav class="flex flex-row pb-4 text-base font-medium text-neutral-500 dark:text-neutral-400">
      <ul class="flex flex-col list-none sm:flex-row">
        
        <li class="flex mb-1 ltr:text-right rtl:text-left sm:mb-0 ltr:sm:mr-7 ltr:sm:last:mr-0 rtl:sm:ml-7 rtl:sm:last:ml-0">
          <a class="decoration-primary-500 hover:underline hover:decoration-2 hover:underline-offset-2 flex items-center" href="/regulamin-uczestnictwa/"
            title="Regulamin uczestnictwa w spotkaniach Python Łódź">
            
            Regulamin uczestnictwa
          </a>
        </li>
        
        <li class="flex mb-1 ltr:text-right rtl:text-left sm:mb-0 ltr:sm:mr-7 ltr:sm:last:mr-0 rtl:sm:ml-7 rtl:sm:last:ml-0">
          <a class="decoration-primary-500 hover:underline hover:decoration-2 hover:underline-offset-2 flex items-center" href="/polityka-cookies/"
            title="Polityka Cookies">
            
            Polityka Cookies
          </a>
        </li>
        
        <li class="flex mb-1 ltr:text-right rtl:text-left sm:mb-0 ltr:sm:mr-7 ltr:sm:last:mr-0 rtl:sm:ml-7 rtl:sm:last:ml-0">
          <a class="decoration-primary-500 hover:underline hover:decoration-2 hover:underline-offset-2 flex items-center" href="/prelegenci-przetwarzanie-danych/"
            title="Informacja o przetwarzaniu danych osobowych prelegenta spotkań Python Łódź">
            
            Przetwarzanie danych osobowych prelegenta
          </a>
        </li>
        
      </ul>
    </nav>
    
  
  <div class="flex items-center justify-between">

    
    
    <p class="text-sm text-neutral-500 dark:text-neutral-400">
      &copy;
      2025
      Python Łódź
    </p>
    

    
    
    <p class="text-xs text-neutral-500 dark:text-neutral-400">
      
      
      Powered by <a class="hover:underline hover:decoration-primary-400 hover:text-primary-500"
        href="https://gohugo.io/" target="_blank" rel="noopener noreferrer">Hugo</a> &amp; <a class="hover:underline hover:decoration-primary-400 hover:text-primary-500"
        href="https://blowfish.page/" target="_blank" rel="noopener noreferrer">Blowfish</a>
    </p>
    

  </div>
  <script>
    
    mediumZoom(document.querySelectorAll("img:not(.nozoom)"), {
      margin: 24,
      background: 'rgba(0,0,0,0.5)',
      scrollOffset: 0,
    })
    
  </script>
  
  
  <script type="text/javascript" src="/js/process.min.ee03488f19c93c2efb199e2e3014ea5f3cb2ce7d45154adb3399a158cac27ca52831db249ede5bb602700ef87eb02434139de0858af1818ab0fb4182472204a4.js" integrity="sha512-7gNIjxnJPC77GZ4uMBTqXzyyzn1FFUrbM5mhWMrCfKUoMdsknt5btgJwDvh&#43;sCQ0E53ghYrxgYqw&#43;0GCRyIEpA=="></script>
  
  
</footer>
<div
  id="search-wrapper"
  class="invisible fixed inset-0 flex h-screen w-screen cursor-default flex-col bg-neutral-500/50 p-4 backdrop-blur-sm dark:bg-neutral-900/50 sm:p-6 md:p-[10vh] lg:p-[12vh]"
  data-url="http://localhost:1313/"
  style="z-index:500"
>
  <div
    id="search-modal"
    class="flex flex-col w-full max-w-3xl min-h-0 mx-auto border rounded-md shadow-lg top-20 border-neutral-200 bg-neutral dark:border-neutral-700 dark:bg-neutral-800"
  >
    <header class="relative z-10 flex items-center justify-between flex-none px-2">
      <form class="flex items-center flex-auto min-w-0">
        <div class="flex items-center justify-center w-8 h-8 text-neutral-400">
          

  <span class="relative block icon">
    <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" class="svg-inline--fa fa-search fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"/></svg>

  </span>


        </div>
        <input
          type="search"
          id="search-query"
          class="flex flex-auto h-12 mx-1 bg-transparent appearance-none focus:outline-dotted focus:outline-2 focus:outline-transparent"
          placeholder="Szukaj"
          tabindex="0"
        />
      </form>
      <button
        id="close-search-button"
        class="flex items-center justify-center w-8 h-8 text-neutral-700 hover:text-primary-600 dark:text-neutral dark:hover:text-primary-400"
        title="Zamknij (Esc)"
      >
        

  <span class="relative block icon">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M310.6 361.4c12.5 12.5 12.5 32.75 0 45.25C304.4 412.9 296.2 416 288 416s-16.38-3.125-22.62-9.375L160 301.3L54.63 406.6C48.38 412.9 40.19 416 32 416S15.63 412.9 9.375 406.6c-12.5-12.5-12.5-32.75 0-45.25l105.4-105.4L9.375 150.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L160 210.8l105.4-105.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-105.4 105.4L310.6 361.4z"/></svg>

  </span>


      </button>
    </header>
    <section class="flex-auto px-2 overflow-auto">
      <ul id="search-results">
        
      </ul>
    </section>
  </div>
</div>

  </div>
</body>

</html>
