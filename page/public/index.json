[{"content": " Informacje # 📅 data: 2025-07-30 🕕 godzina: 18:00 📍 miejsce: IndieBI, Piotrkowska 157A, budynek Hi Piotrkowska ➡️ LINK DO ZAPISÓW ⬅️\nLightning Talk # Chcesz spróbować swoich sił na scenie? Zgł<PERSON>ś swój Lightning Talk już dziś przez formularz - pomoże nam to lepiej zaplanować spotkanie. Możesz też zgłosić się w dniu meetup’u prowadzącemu - czyli 30.07.2025 o 18:00.\nOstateczne potwierdzenie wystąpienia robimy tuż przed prezentacją. Jeśli nie zdążysz przygotować tematu albo coś Ci wypadnie - nic się nie stało!\n🎉 Nagroda! # Dla osób, które poprowadzą Lightning Talk, będą do rozdania dwie wejściówki na tegoroczny PyConPL!\nCzym jest Lightning Talk? # Krótka prezentacja na maksymalnie 5 minut, w której możesz opowiedzieć o czym tylko chcesz (o<PERSON><PERSON><PERSON><PERSON><PERSON> najlepiej, jeśli ma to związek z Pythonem i naszą społecznością).\nZasady # 5 minut na wypowiedź. Nie musisz wykorzystać pełnego czasu - możesz mówić minutę, jeśli chcesz. Dowolna forma - możesz mieć slajdy albo nie. Pokaż stronę projektu, zrób mini live demo, opowiedz historię, a jeśli chcesz - możesz nawet zatańczyć! Tematyka - Python, wszystko co z nim związane, ale też: własne biblioteki, ciekawe lokalne inicjatywy, inspirujące fakapy z pracy, projekty po godzinach. W ostatnich 10 sekundach publiczność zacznie cicho klaskać, żeby dać Ci znać, że zbliżasz się do limitu. Po 5 minutach zaczynamy klaskać głośno - Twój czas się kończy! Zero spiny! Nie oceniamy, wspieramy się nawzajem i bawimy się dobrze. Zgłoszenia do Lightning Talka # Zgłoś swój Lightning Talk już dziś przez formularz - pomoże nam to lepiej zaplanować spotkanie. Możesz też zgłosić się w dniu meetup’u prowadzącemu - czyli 30.07.2025 o 18:00.\nOpen Spaces # Po Lightning Talkach zapraszamy na Open Spaces! To luźna i otwarta forma dyskusji, w której każdy może zaproponować temat do omówienia. Masz coś, o czym chcesz pogadać? Widzisz problem, który warto rozkminić w grupie? Śmiało!\nJak to działa? # Każdy może zgłosić temat, który go interesuje. Robimy głosowanie na wszystkie propozycje - podnosisz rękę dowolną ilość razy, na każdy temat, który według Ciebie jest ciekawy. Dzielimy się na grupy i rozmawiamy o najpopularniejszych tematach. Możesz zmieniać grupy - jeśli jeden temat się wyczerpie, wskakujesz do innej dyskusji. Zero sztywnej agendy - liczy się Twoje zaangażowanie i wymiana doświadczeń. Dlaczego warto? # Open Spaces to świetna okazja, żeby:\npogłębić tematy poruszone na Lightning Talkach, zadać pytania, które nie zmieściły się w prezentacji, poznać ludzi, którzy mają podobne wyzwania i pomysły. Do zobaczenia na scenie!\nSponsorzy # IndieBI Sunscrapers ", "date": "30 lipca 2025", "externalUrl": null, "permalink": "/spotkania/59/", "section": "Spotkania", "summary": "", "title": "Meetup #59", "type": "spotkan<PERSON>"}, {"content": "", "date": "30 lipca 2025", "externalUrl": null, "permalink": "/", "section": "Python Łódź", "summary": "", "title": "Python Łódź", "type": "page"}, {"content": "", "date": "30 lipca 2025", "externalUrl": null, "permalink": "/spotkania/", "section": "Spotkania", "summary": "", "title": "Spotkania", "type": "spotkan<PERSON>"}, {"content": " Informacje # 📅 data: 2025-05-28 🕕 godzina: 18:00 📍 miejsce: IndieBI, Piotrkowska 157A, budynek Hi Piotrkowska ➡️ LINK DO ZAPISÓW ⬅️ 📝 ANKIETA - oceń spotkanie oraz prelekcje\nLive Stream # Prelekcje # Pythonowa konfiguracja, która przyprawi Cię o dreszcze (w dobry spos<PERSON>b, obiecuję!) # Prelegent: <PERSON><PERSON><PERSON><PERSON> — wszyscy jej potrzeb<PERSON>, wszyscy jej nienawidzimy. A mimo to, w każdym projekcie przynajmniej raz udaje nam się ją zepsuć.\nPrzez lata widziałem już wszystko: ręczne pliki konfiguracyjne tworzone dla każdego możliwego środowiska, upychanie setek parametrów w jednym pliku JSON, ręczne odczytywanie zmiennych środowiskowych bez żadnej kontroli typów, c<PERSON> <PERSON>’y wywracające się przez brakujący przecinek. Ale po dekadzie męki w końcu trafiłem na rozwiązanie: pydantic-settings.\nDzięki Pydantic mamy konfigurację, która jest:\n✅ Dokładnie typowana (koniec z zastanawianiem się, czy &ldquo;timeout&rdquo; to rzeczywiście integer!)\n✅ Elastyczna (działa płynnie na lokalnych maszynach, w Dockerze, Kubernetesie i chmurze)\n✅ Łatwa do walidacji (unikniesz awarii w runtime z powodu wpisania „True” zamiast True)\n✅ Świetna do testów (tak, zahaczymy też o sztuczki z pytest)\nALE nie zamierzam tu omawiać podstaw pydantic-settings. Zamiast tego zanurzymy się w zaawansowane typowanie, żeby stworzyć superrestrykcyjną konfigurację, w której nie da się popełnić błędu — taką, która przetrwa dłużej niż jakikolwiek framework JavaScript. Dodatkowo pokażę, jak używać jej w projekcie bez polegania na stanie globalnym, opierając się na sprawdzonych w boju zasadach, które zebrałem przez lata.\nZ tego wystąpienia dowiesz się:\n🎯 Dlaczego większość tradycyjnych metod konfiguracji to strzał w kolano\n🎯 Jak zbudować konfigurację tak solidną, że nic jej nie wytrąci z rytmu\n🎯 Jak porządnie przetestować konfigurację i jej użycie (żeby nie rozpadła się na produkcji)\n🎯 Jakie sekrety Pydantic może jeszcze przed Tobą skrywać\nJeśli uważasz, że konfiguracja jest nudna, spróbuj przesiedzieć tę prezentację i nie poczuć przy tym chociaż odrobiny ekscytacji. Najgorszy scenariusz? Wychodzisz z mniejszą liczbą koszmarów związanych z configiem. Najlepszy? Masz wreszcie konfigurację, która po prostu działa.\nP.S. Te techniki wykraczają poza samą konfigurację — prawdopodobnie wykorzystasz je także w innych częściach swojego projektu! 🚀\nProgramista zoptymalizował aplikację, ale nikt mu nie pogratulował bo była w Pythonie 😔 # Prelegent: Sebastian Buczyński\nWokół tematu wydajności w Pythonie narosło wiele mitów. Rozwiejmy te fałszywe przekonania opierając się na twardych danych.\nPorozmawiajmy jak być lepszym inżynierem oprogramowania w ciągle zmieniającym się świecie, wymagającym podejmowania decyzji i balansowania między różnymi wymaganiami.\nSponsorzy # IndieBI Sunscrapers ", "date": "28 maja 2025", "externalUrl": null, "permalink": "/spotkania/58/", "section": "Spotkania", "summary": "", "title": "Meetup #58", "type": "spotkan<PERSON>"}, {"content": " Informacje # 📅 data: 2025-03-26 🕕 godzina: 18:00 📍 miejsce: IndieBI, Piotrkowska 157A, budynek Hi Piotrkowska ➡️ LINK DO ZAPISÓW ⬅️\nPrelekcje # Czego o product developmencie uczy 5 lat rozwoju własnej aplikacji do journalingu # Prelegent: <PERSON><PERSON><PERSON>z\nRozwijam produkt, którego sam jestem klientem i product ownerem. I na pierwszy rzut oka, to powinno dawać mi natychmiastowy feedback. W końcu nie ma narzutów komunikacyjnych, czekania aż ktoś zaakceptuje jakiś pomysł. A jednak, często idee walidują się miesiącami. Dlaczego tak jest?\n<PERSON>uję dziedzinę, w której stabilny model przekłada się bezpośrednio na mój dobrostan. Precyzyjnie zdefiniowana domena – przełożona na komendy i zapytania – powinna przełoż<PERSON>ć się na rzadko zmieniający się kod, w którym wszystko jest jasno opisane. To dlaczego niektóre funkcje okazały się zbędne po dwóch użyciach, a inne przechodziły już kilka przemian?\nEvent Sourcing czasami wtłaczany jest młodym programistom jak mantra. Immutability is a king, sam nawet się na to złapałem i stwierdziłem “dobra, zobaczmy jak to robi w Django&quot;. W praktyce jednak nadal od czasu do czasu poprawiam rekordy w Django Adminie. A samo wprowadzenie eventów umożliwiło mi głębszą refleksje nad moim życiem, więc okazuje się, że Event Sourcing może dawać nie tylko techniczne korzyści.\nObecnie mam w dzienniku 3380 wpisów. A tekże moje ulubione słowo w jednym z modeli bazy danych: „Rekontekstualizacja”. To historia przypominająca realia wielu start-upów: nie zawsze wiemy, co dokładnie tworzymy, ale działamy pomimo ograniczonego budżetu i czasu.\nCzego się z niej dowiesz?\nGdzie leży różnica między „manage” a „menage”. Co oznacza „pivot” w świecie start-upów. Jak domena może ewoluować w czasie. Jak zamienić „legacy” w „opportunity”. Jak prowadzić dziennik, by korzystnie wpłynął na życie. Detektyw w świecie Pythona # Prelegent: Kamil Kucharski\nWcielimy się w rolę detektywów, którzy z użyciem narzędzi do profilowania odkryją tajemnice tych narzędzi. Opowiem czym są takie narzędzia oraz jak działają. Dodatkowo wspólnie przejdziemy przez różne codebase-y, na których pokaże jak korzystać z tych narzędzi aby efektywnie rozwiązać problem z wydajnością i przyspieszyć działanie naszego kodu. Wszystkie osoby zainteresowane optymalizacją aplikacji i rozwiązywaniem problemów z wydajnością są mile widziane!\nSponsorzy # IndieBI Sunscrapers Zdjęcia # ", "date": "26 marca 2025", "externalUrl": null, "permalink": "/spotkania/57/", "section": "Spotkania", "summary": "", "title": "Meetup #57", "type": "spotkan<PERSON>"}, {"content": " Informacje # 📅 data: 2025-01-29 🕕 godzina: 18:00 📍 miejsce: IndieBI, Piotrkowska 157A, budynek Hi Piotrkowska\nPrelekcje # Nowość w Pythonie 3.14 oraz PyScript # Prelegent: Łukasz Langa\nNagranie # Sponsorzy # IndieBI Sunscrapers ", "date": "29 stycznia 2025", "externalUrl": null, "permalink": "/spotkania/56/", "section": "Spotkania", "summary": "", "title": "Meetup #56", "type": "spotkan<PERSON>"}, {"content": " Informacja o przetwarzaniu danych osobowych prelegenta spotkań Python Łódź # I. Wprowadzenie # W związku z wypełnieniem przez Ciebie formularza zgłoszeniowego prelekcji na spotkanie z cyklu Python Łódź informujemy o zasadach przetwarzania Twoich danych osobowych. II. Administrator Dan<PERSON> i Kontakt # Administratorem danych osobowych prelegentów, w rozumieniu Rozporządzenia Parlamentu Europejskiego i Rady (UE) 2016/679 z dnia 27 kwietnia 2016 r. (dalej: „RODO”), jest <PERSON><PERSON><PERSON><PERSON>, ul. Ks. Biskupa Wincentego Tymienieckiego 16AB/10, 90-365 Łódź, NIP 6612197072, REGON 260600112 (dalej „Organizator”). Możesz skontaktować się z nami za pośrednictwem: adresu e-mail: _____; listownie: ul. Ks. Biskupa Wincentego Tymienieckiego 16AB/10, 90-365 Łódź. III. Informacja o Zgodach # W związku z zadeklarowaniem chęci wzięcia udziału w spotkaniu z cyklu Python Łódź jako prelegent, wyrażasz zgodę na: przetwarzanie Twoich danych osobowych podanych w zgłoszeniu prelekcji na potrzeby organizacji spotkania oraz w celach informacyjnych, reklamowych, promocyjnych i marketingowych wydarzenia; wykorzystanie przedstawionej przez Ciebie prezentacji oraz jej fragmentów w materiałach promocyjnych związanych z cyklem spotkań Python Łódź, w tym na stronie internetowej i w mediach społecznościowych Organizatora; nieodpłatne utrwalanie i wykorzystanie Twojego wizerunku w celach: informacyjnych, promocyjnych i marketingowych cyklu spotkań Python Łódź (zgoda wizerunkowa). Zgoda wizerunkowa obejmuje wszelkie formy publikacji wizerunku, w tym: rozpowszechnianie w sieci Internet, w szczególności zamieszczanie na stronie https://pythonlodz.org/ oraz na kontach społecznościowych związanych z cyklem spotkań Python Łódź (np. w serwisach Facebook, YouTube); zamieszczenie w materiałach promocyjnych lub informacyjnych (np. ulotki, plakaty, broszury). Wyrażenie wyżej wymienionych zgód jest dobrowolne. Zgody możesz odwołać w dowolnym momencie, jednak nie będzie to miało wpływu na zgodność z prawem przetwarzania, którego dokonano na podstawie zgody przed jej wycofaniem. IV. Cele Przetwarzania # Dane osobowe podane w formularzu zgłoszeniowym, takie jak imię, nazwisko, adres e-mail czy numer telefonu, będą przetwarzane jedynie w następujących celach: organizacja spotkania z cyklu Python Łódź, w tym kontakt z prelegentami; publikacja informacji o prelegentach w materiałach promocyjnych i informacyjnych dotyczących wydarzenia. V. Podstawa Prawna Przetwarzania # Podstawą prawną przetwarzania Twoich danych osobowych jest art. 6 ust. 1 lit. a RODO – zgoda na przetwarzanie danych osobowych w celu uczestnictwa w spotkaniu jako prelegent oraz na działania promocyjne związane z wydarzeniem. VI. Prawa Związane z Przetwarzaniem Danych Osobowych # W związku z przetwarzaniem Twoich danych osobowych przysługuje Ci prawo dostępu, sprostowania, ograniczenia, usunięcia, przenoszenia danych osobowych lub ograniczenia ich przetwarzania, a także prawo do wniesienia sprzeciwu wobec przetwarzania. Przysługuje Ci również prawo do wniesienia skargi do organu nadzorczego w sposób i trybie określonym w przepisach RODO. Organem nadzorczym w Polsce jest Prezes Urzędu Ochrony Danych Osobowych. VII. Odbiorcy Danych Osobowych # Twoje dane osobowe mogą zostać udostępnione: podmiotom współpracującym z Organizatorem spotkań, w tym firmom świadczącym usługi techniczne, informatyczne i marketingowe; uczestnikom wydarzenia oraz publiczności, jeżeli prelegent wyrazi zgodę na udostępnienie swoich danych w materiałach promocyjnych. VIII. Informacja o Zautomatyzowanym Podejmowaniu Decyzji # W procesie przetwarzania danych osobowych nie dochodzi do zautomatyzowanego podejmowania decyzji, w tym do profilowania. IX. Pliki Cookies # Informujemy również, że podczas korzystania z formularza zgłoszeniowego na naszej stronie internetowej mogą być wykorzystywane pliki cookies w celu zapewnienia prawidłowego działania strony oraz analizy ruchu. Szczegółowe informacje na temat plików cookies i zasad ich wykorzystywania znajdują się w naszej Polityce Cookies, dostępnej pod adresem: https://pythonlodz.org/polityka-cookies. X. Okres Przetwarzania Danych Osobowych # Twoje dane osobowe nie będą przechowywane dłużej, niż jest to konieczne dla celu, dla którego zostały zebrane, oraz w czasie określonym przepisami prawa. XI. Dobrowolność Podania Danych Osobowych # Podanie danych osobowych jest dobrowolne, ale niezbędne do uczestnictwa w spotkaniu jako prelegent. Jeżeli dane osobowe nie zostaną przez Ciebie podane, uniemożliwi to Twój udział w wydarzeniu jako prelegent. ", "date": "1 stycznia 2025", "externalUrl": null, "permalink": "/prelegenci-przet<PERSON><PERSON><PERSON>-danych/", "section": "Python Łódź", "summary": "", "title": "Informacja o przetwarzaniu danych osobowych prelegenta spotkań Python Łódź", "type": "page"}, {"content": " Polityka Cookies # I. Wprowadzenie # Niniejsza Polityka Cookies określa zasady zapisywania i uzyskiwania dostępu do informacji na urządzeniach użytkowników korzystających z witryny (dalej „Użytkownicy”), za pomocą plików cookies, wykorzystywanych przez Grzegorz Kocjan Software (dalej „Organizator”). Polityka ma na celu poinformowanie Użytkowników o sposobach wykorzystywania plików cookies, w tym Google Analytics oraz Piksel Facebooka, a także o możliwościach zarządzania nimi w zakresie ustawień przeglądarki internetowej. II. Definicje i Rodzaje Plików Cookies # Cookies to niewielkie pliki tekstowe, które są zapisywane na urządzeniu końcowym Użytkownika (komputerze, smartfonie, tablecie) podczas przeglądania stron internetowych. W ramach witryny mogą być stosowane następujące rodzaje plików cookies: Cookies sesyjne (session cookies): pliki tymczasowe, przechowywane w pamięci przeglądarki do momentu wylogowania, opuszczenia strony lub zamknięcia przeglądarki. Cookies trwałe (persistent cookies): pliki przechowywane na urządzeniu Użytkownika przez określony w parametrach cookies czas lub do momentu ręcznego usunięcia. Cookies własne (first-party cookies): ustawiane bezpośrednio przez stronę internetową, którą Użytkownik odwiedza. Cookies zewnętrzne (third-party cookies): ustawiane przez zewnętrzne serwisy, z których funkcjonalności strona korzysta (np. Google Analytics, Piksel Facebooka). III. Cele Wykorzystania Plików Cookies # Zapewnienie prawidłowego działania witryny i dostosowanie jej funkcjonalności do potrzeb Użytkowników (np. zapamiętywanie preferencji). Ułatwienie logowania, wypełniania formularzy oraz innych funkcji serwisu. Tworzenie anonimowych statystyk pozwalających zrozumieć, w jaki sposób Użytkownicy korzystają z witryny, co umożliwia ulepszanie jej struktury i zawartości. Prowadzenie działań marketingowych i remarketingowych, w tym mierzenie skuteczności kampanii reklamowych. IV. Google Analytics # Google Analytics to usługa analizy ruchu na stronach internetowych, świadczona przez Google LLC. Pozwala ona zbierać informacje o sposobie korzystania z witryny przez Użytkowników, m.in. o odwiedzanych podstronach, czasie spędzonym na stronie, czy geolokalizacji. Dane zebrane przez Google Analytics służą do tworzenia raportów i analiz ruchu w witrynie, co pomaga doskonalić jej strukturę i zawartość. Google może przekazywać te informacje podmiotom trzecim, jeśli będzie do tego zobowiązana na mocy przepisów prawa lub jeśli takie podmioty przetwarzają dane w imieniu Google. Użytkownik może zablokować gromadzenie danych przez Google Analytics, instalując w przeglądarce odpowiednie rozszerzenie (np. dodatek do przeglądarki blokujący Google Analytics). V. Piksel Facebooka # Piksel Facebooka (Facebook Pixel) to narzędzie analityczne służące do mierzenia efektywności reklam i poznania działań Użytkowników na stronie, kiedy zostali przekierowani z reklamy na Facebooku. Piksel Facebooka umożliwia kierowanie spersonalizowanych reklam do osób, które wcześniej odwiedziły witrynę lub podjęły konkretne czynności (np. zapisanie się na newsletter). Informacje zebrane za pomocą Piksela Facebooka są przekazywane i zapisywane przez Facebook Inc. Użytkownik może zapoznać się z ustawieniami prywatności w serwisie Facebook, w tym zmieniać swoje preferencje reklamowe bezpośrednio w ustawieniach konta Facebook. VI. Zarządzanie Plikami Cookies # Użytkownik może samodzielnie i w każdym czasie zmienić ustawienia dotyczące plików cookies, określając warunki ich przechowywania i uzyskiwania dostępu do urządzenia Użytkownika. Zmiany ustawień plików cookies można dokonać za pomocą ustawień przeglądarki internetowej. Ograniczenie stosowania plików cookies może wpłynąć na niektóre funkcjonalności dostępne w witrynie i utrudnić korzystanie z niej. Brak zmiany ustawień przeglądarki w zakresie cookies oznacza akceptację dla stosowanych w witrynie plików cookies. VII. Zmiany w Polityce Cookies # Organizator zastrzega sobie prawo do wprowadzania zmian w niniejszej Polityce Cookies. Zmiany mogą wynikać m.in. ze zmian przepisów prawa lub dodania nowych funkcjonalności w witrynie. Informacja o zmianach zostanie zamieszczona na stronie internetowej w formie zaktualizowanej Polityki Cookies. Nowa wersja Polityki obowiązuje od momentu jej publikacji. Data ostatniej aktualizacji: 2025-01-01\n", "date": "1 stycznia 2025", "externalUrl": null, "permalink": "/polityka-cookies/", "section": "Python Łódź", "summary": "", "title": "Polityka Cookies", "type": "page"}, {"content": " Regulamin uczestnictwa w spotkaniach Python Łódź # I. Postanowienia Ogólne # Niniej<PERSON><PERSON> regulamin (dalej „Regulamin”) określa zasady uczestnictwa w spotkaniach organizowanych przez Grzegorz Kocjan Software z siedzibą w Ł<PERSON>zi (90-365), ul. Ks. Biskupa Wincentego Tymienieckiego 16AB/10, NIP 6612197072, REGON 260600112 (dalej „Organizator”), zwi<PERSON>zan<PERSON> z edukacją w zakresie programowania w języku Python (dalej „Spotkanie”). Spotkania są skierowane do wszystkich osób zainteresowanych rozwijaniem swoich umiejętności programistycznych, niezależnie od poziomu zaawansowania. Uczestnicy oraz Prelegenci, którzy zdecydują się wziąć udział w Spotkaniu, akceptują niniejszy Regulamin i zobowiązują się do jego przestrzegania. II. Warunki Uczestnictwa # Uczestnikiem Spotkania jest każda osoba, która znajdzie się w miejscu organizacji Spotkania z zamiarem uczestnictwa w nim (dalej „Uczestnik”). Uczestnikiem może być tylko osoba pełnoletnia posiadająca pełną zdolność do czynności prawnych. Uczestnictwo w Spotkaniach jest bezpłatne i nie wymaga wcześniejszej rejestracji. Liczba miejsc na każde Spotkanie jest nieograniczona, jednak Organizator może ograniczyć liczbę Uczestników z powodu działania siły wyższej, a także z ważnych powodów, w szczególności ze względów lokalowych lub higieniczno-sanitarnych. III. Zasady dla Prelegentów # Prelegentem podczas Spotkania może zostać tylko osoba pełnoletnia posiadająca pełną zdolność do czynności prawnych, która jednocześnie posiada odpowiednie kompetencje do prowadzenia prezentacji w zakresie zgłoszonego tematu (dalej „Prelegent”). Zgłoszenia Prelegentów są przyjmowane na podstawie wypełnionego formularza dostępnego na stronie internetowej Organizatora, tj. https://pythonlodz.org/. Organizator zastrzega sobie prawo do akceptacji lub odrzucenia zgłoszenia Prelegenta bez podania przyczyny. Prelegent zobowiązuje się do rzetelnego i profesjonalnego przekazywania wiedzy oraz szanowania Uczestników Spotkania. Organizator nie ponosi odpowiedzialności za treści przekazywane przez Prelegenta podczas Spotkania oraz za jakiekolwiek szkody wynikłe z zastosowania się do zaleceń, rekomendacji lub wskazówek Prelegenta. Organizator nie ponosi odpowiedzialności za jakiekolwiek negatywne skutki wynikłe z wykorzystania treści przekazanych przez Prelegenta podczas Spotkania. IV. Obowiązki Uczestników i Prelegentów # Każdy Uczestnik oraz Prelegent jest zobowiązany do zachowania zgodnego z zasadami współżycia społecznego, w szczególności do poszanowania innych uczestników Spotkania, ich poglądów oraz poziomu wiedzy. Zabronione jest jakiekolwiek agresywne zachowanie, które mogłoby zakłócić przebieg Spotkania, w tym używanie wulgaryzmów lub przemocy. Uczestników oraz Prelegentów obowiązuje zakaz wnoszenia i używania w miejscu organizacji Spotkania broni, amunicji, materiałów pirotechnicznych, szkodliwych substancji chemicznych, a także ognia otwartego, mogących stanowić zagrożenie pożarowe, powodować uszkodzenia mienia lub stanowiących niebezpieczeństwo dla zdrowia i życia osób, a także zakaz wnoszenia i spożywania alkoholu oraz środków odurzających, palenia wyrobów tytoniowych oraz używania papierosów elektronicznych. Uczestnicy oraz Prelegenci są zobowiązani do przestrzegania harmonogramu Spotkania oraz szanowania czasu innych osób. Wszelkie materiały dydaktyczne oraz treści przedstawiane podczas Spotkań są własnością Organizatora lub Prelegenta, chyba że wskazano inaczej. Powielanie, rozpowszechnianie lub wykorzystywanie tych materiałów bez zgody autora jest zabronione. V. Polityka Prywatności i Dane Osobowe # Rejestrując się na Spotkania, Prelegenci wyrażają zgodę na przetwarzanie swoich danych osobowych przez Organizatora w celu organizacji i przeprowadzenia Spotkania. Organizator zobowiązuje się do ochrony danych osobowych Uczestników i Prelegentów zgodnie z obowiązującymi przepisami prawa. Przebieg Spotkań cyklu Python Łódź może być utrwalany poprzez wykonywanie fotografii lub nagrań wideo, w tym może być prowadzona transmisja na żywo z wydarzenia. Na tych fotografiach, nagraniach lub transmisji może być utrwalony wizerunek Uczestników lub Prelegentów. Materiały te zostaną wykonane spontanicznie i mogą zostać wykorzystywane przez Organizatora do celów promocyjnych i dokumentacyjnych, w tym opublikowane na stronach internetowych, w mediach społecznościowych oraz innych kanałach związanych z wydarzeniami Python Łódź lub konkretnym Spotkaniem, którego dotyczy fotografia lub nagranie wideo albo transmisja. Uczestnictwo w Spotkaniu oznacza wyrażenie zgody na utrwalanie i publikację wizerunku. Rozpowszechnianie wizerunku odbywa się na podstawie art. 81 ust. 2 pkt 2 ustawy o prawie autorskim i prawach pokrewnych. Jeżeli nie wyrażasz zgody na utrwalanie wizerunku, prosimy o kontakt z nami przed rozpoczęciem Spotkania. Administratorem danych osobowych Uczestników w zakresie wizerunku jest Organizator. Organizator przetwarza dane osobowe w zakresie wizerunku wyłącznie w celach informacyjnych, dokumentacyjnych, promocyjnych i reklamowych Spotkań. Takie przetwarzanie odbywa się na podstawie prawnie uzasadnionego interesu Administratora (art. 6 ust. 1 lit. f RODO). Każdemu przysługuje prawo wniesienia sprzeciwu wobec dalszego przetwarzania jego danych osobowych oraz prawo wniesienia skargi do Prezesa Urzędu Ochrony Danych Osobowych, gdy uzna, że przetwarzanie danych osobowych narusza przepisy RODO. Każdy Uczestnik ma prawo dostępu do treści swoich danych, prawo do ich sprostowania, prawo do usunięcia danych lub ograniczenia ich przetwarzania, prawo do przenoszenia danych oraz prawo do cofnięcia zgody w dowolnym momencie bez wpływu na zgodność z prawem przetwarzania, którego dokonano na podstawie zgody przed jej cofnięciem. Organizator nie odpowiada za rozpowszechnianie wizerunku dokonywane przez innych Uczestników i osoby trzecie bez zgody właścicieli wizerunku. Organizator wzywa do powstrzymania się od utrwalania wizerunku konkretnych obcych osób bez ich zgody, a szczególnie przed jego udostępnianiem w Internecie. VI. Częstotliwość, Zmiany i Odwołania Spotkań # Organizator dokłada starań, aby Spotkania odbywały się regularnie, zazwyczaj raz na dwa miesiące, w ostatnią środę. Organizator zastrzega możliwość zorganizowania Spotkań w innym terminie. Organizator zastrzega sobie prawo do odwołania Spotkania lub zmiany jego formy (np. na Spotkanie online). O zmianach wskazanych w ust. 2 powyżej Organizator poinformuje na stronie internetowej https://pythonlodz.org/. VII. Postanowienia Końcowe # Organizator nie ponosi odpowiedzialności za rzeczy osobiste pozostawione na miejscu Spotkania ani za ewentualne uszkodzenie mienia Uczestników czy Prelegentów podczas Spotkania (w tym uszkodzenia sprzętu wynikające z podłączania urządzeń do sieci lub rzutnika). W sprawach nieuregulowanych niniejszym Regulaminem zastosowanie mają przepisy prawa powszechnie obowiązującego. Organizator zastrzega sobie prawo do wprowadzenia zmian w Regulaminie; zmiany wchodzą w życie po ich opublikowaniu na stronie internetowej Organizatora. Wszelkie pytania i uwagi dotyczące Spotkań można kierować do Organizatora na adres e-mail wskazany na stronie https://pythonlodz.org/. Akceptacja niniejszego Regulaminu jest warunkiem uczestnictwa w wydarzeniach organizowanych przez Grzegorz Kocjan Software.\n", "date": "1 stycznia 2025", "externalUrl": null, "permalink": "/regulamin-uczestnictwa/", "section": "Python Łódź", "summary": "", "title": "Regulamin uczestnictwa w spotkaniach Python Łódź", "type": "page"}, {"content": "", "date": "1 stycznia 2025", "externalUrl": null, "permalink": "/tags/regulaminy/", "section": "Tags", "summary": "", "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "tags"}, {"content": "", "date": "1 stycznia 2025", "externalUrl": null, "permalink": "/tags/", "section": "Tags", "summary": "", "title": "Tags", "type": "tags"}, {"content": " Informacje # 📅 data: 2024-11-27 🕕 godzina: 18:00 📍 miejsce: IndieBI, Piotrkowska 157A, budynek Hi Piotrkowska\nPrelekcje # Zabawa z Pydantic i pattern matching # Prelegent: <PERSON> matching jest z nami od wersji Python 3.10. Od momentu wprowadzenia instrukcji match-case, otrzymaliśmy potężne i eleganckie narzędzie do kontroli przepływu programu. Ta prezentacja ma na celu pokazanie rzeczywistego scenariusza obsługi różnych wiadomości pochodzących od brokera, przy użyciu match-case i Pydantic.\nNagranie # Jak logi mogą ocalić Twój dzień? Czyli Loguru w akcji # Prelegent: <PERSON>\nW tej prezentacji omówimy różnice między standardowym loggerem a Loguru, p<PERSON><PERSON><PERSON><PERSON>, jak efektywnie wykorzystać ustrukturyzowane logi w projekcie oraz podkreślimy znaczenie logów w procesie tworzenia oprogramowania dla zapewnienia wysokiej jakości i niezawodności aplikacji.\nNagranie # Sponsorzy # IndieBI Sunscrapers ", "date": "27 listopada 2024", "externalUrl": null, "permalink": "/spotkania/55/", "section": "Spotkania", "summary": "", "title": "Meetup #55", "type": "spotkan<PERSON>"}, {"content": "", "externalUrl": null, "permalink": "/authors/", "section": "Authors", "summary": "", "title": "Authors", "type": "authors"}, {"content": "", "externalUrl": null, "permalink": "/categories/", "section": "Categories", "summary": "", "title": "Categories", "type": "categories"}, {"content": " Opis # IndieBI to pier<PERSON><PERSON> w swoim rodzaju platforma do agregacji i analizy danych dla gier wideo na komputery PC i konsole. Zapewnia studiom gier zestaw narzędzi – aplikacje internetowe i komputerowe, wizual<PERSON><PERSON><PERSON> da<PERSON>, raporty analityczne, które pomagają zrozumieć ich informacje sprzedażowe i marketingowe, analizować je pod kątem cennych trendów i wzorców oraz zapewniać praktyczne porady w celu optymalizacji sprzedaży i przychodów. Pomaga studiom i wydawcom prowadzić mądrzejsze biznesy. Pomaga im to sprzedawać więcej gier. I nigdy wcześniej tego nie robiono.\nTe wszystkie narzędzia nie powstałyby gdyby nie nasze zespoły produktowe. Wszyscy pracujemy ramię w ramię, <PERSON><PERSON><PERSON> dostarczyć użytkownikom prawdziwą wartość. <PERSON><PERSON><PERSON> zespół ma pełną wol<PERSON>ć, jeśli chodzi o ustalanie własnej kultury, rytuałów i tego, jak chce działać nie zapominając o ciągłym rozwoju. Raz w miesiącu odbywają się w naszym biurze Hackdaye - jest to cały dzień poświęcony aktywnościom samorozwojowym, wspólnym warsztatom, kończący się wspólną imprezą w biurze.\n", "externalUrl": null, "permalink": "/sponsorzy/indiebi/", "section": "Sponsorz<PERSON>", "summary": "", "title": "IndieBI", "type": "sponsorzy"}, {"content": "", "externalUrl": null, "permalink": "/series/", "section": "Series", "summary": "", "title": "Series", "type": "series"}, {"content": " Zostań sponsorem # Zostań naszym sponsorem i współtwórz społeczność Pythonową!\nDzięki sponsorom możemy organizować regularne meetupy, zapraszać ciekawych prelegentów oraz zapewniać przestrzeń do wspólnej nauki i networkingu. Dla naszych Partnerów to doskonała okazja, by:\nZaprezentować swoją markę szerokiej grupie specjalistów IT i pasjonatów Pythona. Zbudować wizerunek firmy jako aktywnego promotora rozwoju technologicznego i społecznościowego. Zrekrutować utalentowanych programistów i nawiązać relacje z osobami głodnymi wiedzy i nowych projektów. Wspierać lokalną społeczność, która stale się rozwija i chętnie dzieli wiedzą. Jeśli chcesz być częścią dynamicznie rosnącej społeczności Python Łódź i przyczynić się do jej rozwoju – dołącz do nas jako sponsor! Wspólnie zadbamy o to, by ka<PERSON><PERSON> spotkanie oferowało solidną dawkę wiedzy, inspiracji i niezapomnianą atmosferę.\nNasi aktualni sponsorzy # ", "externalUrl": null, "permalink": "/sponsorzy/", "section": "Sponsorz<PERSON>", "summary": "", "title": "Sponsorz<PERSON>", "type": "sponsorzy"}, {"content": " Opis # Sunscrapers to warszawska firma technologiczna realizująca projekty z obszaru web developmentu, data engineeringu i AI dla wiodących funduszy inwestycyjnych i scale’upów.\nNasz zespół liczy ponad 40 doświadczonych i utalentowanych specjalistów, których łączy wspólny cel: dostarczanie najwyższej jakości oprogramowania w oparciu o partnerskie relacje z naszymi klientami.\nStawiamy na kulturę pracy opartą na rozwoju, zaufaniu i zaangażowaniu. Wspieramy community poprzez eventy, angażujemy się w projekty open-source oraz prowadzimy wewnętrzny dział R&amp;D.\nPoznaj nas - odwiedź naszą www: https://sunscrapers.com/\nZobacz, jak (dobrze!) oceniają nas współpracownicy: Glassdoor 🙂\n", "externalUrl": null, "permalink": "/sponsorzy/sunscrapers/", "section": "Sponsorz<PERSON>", "summary": "", "title": "Sunscrapers", "type": "sponsorzy"}]