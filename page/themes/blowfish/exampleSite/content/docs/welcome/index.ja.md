---
title: "Blowfish へようこそ"
weight: 1
draft: false
description: "Blowfish バージョン 2.0 の新機能をご紹介。"
tags: ["new", "docs"]
series: ["Documentation"]
series_order: 1
---

{{< lead >}}
Blowfish にはたくさんの機能が詰まっています。
{{< /lead >}}

元々、Blowfish の目標は、シンプルで軽量なテーマを開発することでした。このテーマは <a target="_blank" href="https://github.com/nunocoracao/congo">Congo</a> をフォークし、その当初のビジョンを発展させたものです。

## Tailwind CSS 3.0

Tailwind CSS は Blowfish の中核であり、今回のリリースには最新の [Tailwind CSS バージョン 3](https://tailwindcss.com/blog/tailwindcss-v3) が含まれています。パフォーマンスの最適化と、いくつかの素晴らしい新しい CSS 機能のサポートが追加されています。

{{< youtube "TmWIrBPE6Bc" >}}

## 多言語対応

多くの要望が寄せられていた多言語対応が Blowfish に追加されました！コンテンツを複数の言語で公開すると、サイトは利用可能なすべての翻訳で構築されます。

<div class="text-2xl text-center" style="font-size: 2.8rem">🇬🇧 🇩🇪 🇫🇷 🇪🇸 🇨🇳 🇧🇷 🇹🇷 🇧🇩</div>

コミュニティからの貢献のおかげで、Blowfish はすでに[30の言語](https://github.com/nunocoracao/blowfish/tree/main/i18n)に翻訳されており、今後も追加される予定です。ちなみに、新しい言語の[プルリクエスト](https://github.com/nunocoracao/blowfish/pulls)はいつでも大歓迎です！

## RTL 言語対応

新しい Tailwind と多言語機能の利点の1つは、RTL 言語サポートを追加できることです。有効にすると、サイト全体のコンテンツが右から左に並べ替えられます。テーマ内のすべての要素が、このモードで見栄えが良くなるように再スタイルされており、RTL 言語でコンテンツを生成したい制作者を支援します。

RTL は言語ごとに制御されるため、プロジェクト内で RTL と LTR の両方のコンテンツを混在させることができ、テーマはそれに応じて対応します。

## 自動画像リサイズ

Blowfish 2.0 の大きな変更点は、自動画像リサイズ機能の追加です。Hugo Pipes の力を使って、Markdown コンテンツ内の画像が自動的に異なる出力サイズにスケーリングされるようになりました。これらは HTML の `srcset` 属性を使用して表示され、最適化されたファイルサイズをサイト訪問者に提供できます。

![](image-resizing.png)

```html
<!-- Markdown: ![私の画像](image.jpg) -->
<img
  srcset="
    /image_320x0_resize_q75_box.jpg 320w,
    /image_635x0_resize_q75_box.jpg 635w,
    /image_1024x0_resize_q75_box.jpg 1024w,
    /image_1270x0_resize_q75_box.jpg 2x"
  src="/image_635x0_resize_q75_box.jpg"
  alt="私の画像"
/>
```

何よりも、何も変更する必要はありません！標準の Markdown 画像構文を挿入するだけで、あとはテーマが処理してくれます。もう少し細かく制御したい場合は、`figure` ショートコードが完全に書き変えれば、同じリサイズ機能が利用できます。

## サイト内検索

[Fuse.js](https://fusejs.io) を搭載したサイト内検索により、訪問者はコンテンツをすばやく簡単に見つけることができます。すべての検索はクライアント側で実行されるため、サーバー側で設定する必要はなく、クエリは非常に高速に実行されます。サイト設定で機能を有効にするだけで、準備完了です。もちろん、完全なキーボードナビゲーションもサポートされています！

## 目次

多くの要望が寄せられていた機能である、記事ページに目次を表示できるようになりました。このページで実際に動作を確認できます。目次は完全にレスポンシブ対応で、さまざまな画面解像度で利用可能なスペースを活用するように調整されます。

グローバルまたは記事ごとに利用可能な目次は、標準の Hugo 設定値を使用して完全にカスタマイズでき、プロジェクトに合わせて動作を調整できます。

## アクセシビリティの改善

ARIA 属性説明をより多くの項目に追加したり、特定のテキスト要素のコントラストを調整したりするなど、今回のリリースはこれまでで最もアクセシブルなものとなっています。

バージョン2では、「コンテンツへスキップ」リンクと「トップへスクロール」リンクも導入され、すばやく移動できるようになりました。マウスを使わずに検索などの項目を有効にするためのキーボードショートカットもあります。

新しい画像リサイズ機能では、`alt` 属性と `title` 属性を完全に制御できるため、すべての訪問者にアクセシブルなエクスペリエンスを提供できます。

## その他にもたくさん

他にもたくさんの新機能があります。記事やリストページにタクソノミーを表示したり、新しい `headline` 著者パラメータを使用してホームページをカスタマイズしたりできます。また、JSON-LD 構造化データが改善され、SEO パフォーマンスがさらに最適化されています。
