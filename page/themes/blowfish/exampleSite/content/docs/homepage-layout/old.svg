<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin:auto;background:rgba(NaN, NaN, NaN, 0);display:block;z-index:1;position:relative" width="1084" height="322" preserveAspectRatio="xMidYMid" viewBox="0 0 1084 322">
    <defs>
      <pattern id="pid-0.1520523902861448" x="0" y="0" width="665.6" height="665.6" patternUnits="userSpaceOnUse">
        <g transform="scale(2.6)"><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#93dbe9" stroke-width="10" transform="translate(0 -36.57142857142857)">
      <animateTransform attributeName="transform" type="translate" values="256 -36.57142857142857;0 -36.57142857142857;256 -36.57142857142857" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="0s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#689cc5" stroke-width="10" transform="translate(0 0)">
      <animateTransform attributeName="transform" type="translate" values="0 0;256 0;0 0" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="-1.4285714285714284s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#5e6fa3" stroke-width="10" transform="translate(0 36.57142857142857)">
      <animateTransform attributeName="transform" type="translate" values="256 36.57142857142857;0 36.57142857142857;256 36.57142857142857" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="-2.8571428571428568s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#3b4368" stroke-width="10" transform="translate(0 73.14285714285714)">
      <animateTransform attributeName="transform" type="translate" values="0 73.14285714285714;256 73.14285714285714;0 73.14285714285714" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="-4.285714285714286s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#191d3a" stroke-width="10" transform="translate(0 109.71428571428571)">
      <animateTransform attributeName="transform" type="translate" values="256 109.71428571428571;0 109.71428571428571;256 109.71428571428571" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="-5.7142857142857135s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#d9dbee" stroke-width="10" transform="translate(0 146.28571428571428)">
      <animateTransform attributeName="transform" type="translate" values="0 146.28571428571428;256 146.28571428571428;0 146.28571428571428" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="-7.142857142857142s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#b3b7e2" stroke-width="10" transform="translate(0 182.85714285714286)">
      <animateTransform attributeName="transform" type="translate" values="256 182.85714285714286;0 182.85714285714286;256 182.85714285714286" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="-8.571428571428571s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path><path d="M -256 9.314285714285713 C -192 9.314285714285713 -192 2 -128 2 C -64 2 -64 9.314285714285713 2 9.314285714285713 C 64 9.314285714285713 64 2 128 2 C 192 2 192 9.314285714285713 256 9.314285714285713 L 384 384 L -384 384 L -384 9.314285714285713 Z" fill="none" stroke="#93dbe9" stroke-width="10" transform="translate(0 219.42857142857142)">
      <animateTransform attributeName="transform" type="translate" values="0 219.42857142857142;256 219.42857142857142;0 219.42857142857142" keyTimes="0;0.5;1" dur="20s" repeatCount="indefinite" calcMode="spline" begin="0s" keySplines="0.5 0 0.5 1;0.5 0 0.5 1"></animateTransform>
    </path></g>
      </pattern>
    </defs>
    <rect x="0" y="0" width="1084" height="322" fill="url(#pid-0.1520523902861448)"></rect>
    </svg>