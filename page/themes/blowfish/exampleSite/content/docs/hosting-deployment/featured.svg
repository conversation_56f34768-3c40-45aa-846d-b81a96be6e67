<svg id="canvas" height="100%" xmlns="http://www.w3.org/2000/svg" version="1.1"
    xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs"
    viewBox="0 0 600 600">
    <rect width="600" height="600" fill="transparent"></rect>
    <mask id="mask" mask-type="alpha" maskUnits="userSpaceOnUse">
        <rect transform="translate(0, 0)" width="600" height="600" fill="white"></rect>
    </mask>
    <svg>
        <path transform="translate(0, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(0, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(0, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(0, 60) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(0, 60) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(0, 60) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(0, 120)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(0, 120)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(0, 180) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(0, 180) translate(30,30) rotate(0) translate(-30,-30)"
            fill="#90e0ef" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(0, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(0, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#0096c7" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(0, 300) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(0, 300) translate(30,30) rotate(270) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#023e8a"></path>
        <path transform="translate(0, 300) translate(30,30) rotate(270) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(0, 360) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(0, 360) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#0096c7" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(0, 420) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(0, 420) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(0, 420) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(0, 480)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(0, 480)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(0, 540)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <path transform="translate(60, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(60, 0) translate(30,30) rotate(270) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#00b4d8"></path>
        <path transform="translate(60, 0) translate(30,30) rotate(270) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(60, 60)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <rect transform="translate(60, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" x="0" y="0" width="60" height="60" stroke="#48cae4" stroke-width="2"></rect>
        <path transform="translate(60, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" d="M0 0L60 60" stroke="#48cae4" stroke-width="2"></path>
    </svg>
    <svg>
        <path transform="translate(60, 180) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(60, 180) translate(30,30) rotate(0) translate(-30,-30)"
            fill="#90e0ef" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(60, 240)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <path transform="translate(60, 300)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(60, 300)" cx="30" cy="30" r="30" fill="#48cae4"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(60, 360)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <path transform="translate(60, 420)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(60, 420)" cx="30" cy="30" r="30" fill="#48cae4"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(60, 480)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(60, 480)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(60, 540)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(60, 540)" cx="30" cy="30" r="30" fill="#48cae4"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(120, 0)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(120, 0)" cx="30" cy="30" r="30" fill="#023e8a" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(120, 60) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(120, 60) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(120, 60) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <rect transform="translate(120, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" x="0" y="0" width="60" height="60" stroke="#48cae4" stroke-width="2"></rect>
        <path transform="translate(120, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" d="M0 0L60 60" stroke="#48cae4" stroke-width="2"></path>
    </svg>
    <svg>
        <path transform="translate(120, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(120, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(120, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#90e0ef" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(120, 240) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(120, 240) translate(30,30) rotate(180) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#ade8f4"></path>
        <path transform="translate(120, 240) translate(30,30) rotate(180) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(120, 300) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(120, 300) translate(30,30) rotate(270) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
        <path transform="translate(120, 300) translate(30,30) rotate(270) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(120, 360) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(120, 360) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#48cae4" stroke="#48cae4" stroke-width="2" d="M30 0h30v60h-30z"></path>
        <path transform="translate(120, 360) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(120, 420)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(120, 420)" cx="30" cy="30" r="30" fill="#90e0ef"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(120, 480) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(120, 480) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(120, 480) translate(30,30) rotate(180) translate(-30,-30)"
            fill="#ade8f4" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(120, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(120, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(120, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(180, 0) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(180, 0) translate(30,30) rotate(180) translate(-30,-30)"
            fill="#caf0f8" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(180, 60) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(180, 60) translate(30,30) rotate(0) translate(-30,-30)"
            fill="#023e8a" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(180, 120) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(180, 120) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(180, 120) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(180, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(180, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#ade8f4" stroke="#48cae4" stroke-width="2" d="M30 0h30v60h-30z"></path>
        <path transform="translate(180, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(180, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(180, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(180, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(180, 300) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(180, 300) translate(30,30) rotate(0) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
        <path transform="translate(180, 300) translate(30,30) rotate(0) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(180, 360)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <rect transform="translate(180, 420) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" x="0" y="0" width="60" height="60" stroke="#48cae4" stroke-width="2"></rect>
        <path transform="translate(180, 420) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" d="M0 0L60 60" stroke="#48cae4" stroke-width="2"></path>
    </svg>
    <svg>
        <path transform="translate(180, 480)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <path transform="translate(180, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(180, 540) translate(30,30) rotate(180) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#0077b6"></path>
        <path transform="translate(180, 540) translate(30,30) rotate(180) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <rect transform="translate(240, 0) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" x="0" y="0" width="60" height="60" stroke="#48cae4" stroke-width="2"></rect>
        <path transform="translate(240, 0) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" d="M0 0L60 60" stroke="#48cae4" stroke-width="2"></path>
    </svg>
    <svg>
        <path transform="translate(240, 60)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(240, 60)" cx="30" cy="30" r="30" fill="#0096c7"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(240, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(240, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(240, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#90e0ef" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(240, 180) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(240, 180) translate(30,30) rotate(270) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#48cae4"></path>
        <path transform="translate(240, 180) translate(30,30) rotate(270) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(240, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(240, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#03045e" stroke="#48cae4" stroke-width="2" d="M30 0h30v60h-30z"></path>
        <path transform="translate(240, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(240, 300) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(240, 300) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(240, 300) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(240, 360) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(240, 360) translate(30,30) rotate(180) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#00b4d8"></path>
        <path transform="translate(240, 360) translate(30,30) rotate(180) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(240, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(240, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#90e0ef" stroke="#48cae4" stroke-width="2" d="M30 0h30v60h-30z"></path>
        <path transform="translate(240, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(240, 480)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(240, 480)" cx="30" cy="30" r="30" fill="#48cae4"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(240, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(240, 540) translate(30,30) rotate(180) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#00b4d8"></path>
        <path transform="translate(240, 540) translate(30,30) rotate(180) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(300, 0) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 0) translate(30,30) rotate(0) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#00b4d8"></path>
        <path transform="translate(300, 0) translate(30,30) rotate(0) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(300, 60)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(300, 60)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(300, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 120) translate(30,30) rotate(90) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#023e8a"></path>
        <path transform="translate(300, 120) translate(30,30) rotate(90) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(300, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(300, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(300, 240) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 240) translate(30,30) rotate(270) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#0077b6"></path>
        <path transform="translate(300, 240) translate(30,30) rotate(270) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(300, 300) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 300) translate(30,30) rotate(90) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#48cae4"></path>
        <path transform="translate(300, 300) translate(30,30) rotate(90) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(300, 360) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 360) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(300, 360) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(300, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(300, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(300, 480) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 480) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(300, 480) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(300, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(300, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(300, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="#0096c7" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#03045e" stroke="#48cae4" stroke-width="2" d="M30 0h30v60h-30z"></path>
        <path transform="translate(360, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 60) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 60) translate(30,30) rotate(0) translate(-30,-30)"
            fill="#48cae4" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 120)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(360, 120)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(360, 180) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 180) translate(30,30) rotate(180) translate(-30,-30)"
            fill="#caf0f8" stroke="#48cae4" stroke-width="2" d="M30 0h30v60h-30z"></path>
        <path transform="translate(360, 180) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#ade8f4" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 300) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 300) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(360, 300) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 360) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 360) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(360, 360) translate(30,30) rotate(180) translate(-30,-30)"
            fill="#0096c7" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 420)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(360, 420)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(360, 480) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 480) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#0096c7" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(360, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(360, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(360, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(420, 0) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(420, 0) translate(30,30) rotate(0) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#00b4d8"></path>
        <path transform="translate(420, 0) translate(30,30) rotate(0) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(420, 60) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(420, 60) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(420, 60) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(420, 120)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(420, 120)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(420, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(420, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(420, 180) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#90e0ef" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(420, 240) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(420, 240) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#ade8f4" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(420, 300)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <path transform="translate(420, 360) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(420, 360) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(420, 360) translate(30,30) rotate(90) translate(-30,-30)"
            fill="#caf0f8" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(420, 420)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <path transform="translate(420, 480)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(420, 480)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(420, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(420, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(420, 540) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(480, 0) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(480, 0) translate(30,30) rotate(90) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
        <path transform="translate(480, 0) translate(30,30) rotate(90) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(480, 60)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(480, 60)" cx="30" cy="30" r="30" fill="#03045e"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(480, 120) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(480, 120) translate(30,30) rotate(0) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#0077b6"></path>
        <path transform="translate(480, 120) translate(30,30) rotate(0) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(480, 180) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(480, 180) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(480, 180) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(480, 240) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(480, 240) translate(30,30) rotate(90) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#0096c7"></path>
        <path transform="translate(480, 240) translate(30,30) rotate(90) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(480, 300)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
    </svg>
    <svg>
        <path transform="translate(480, 360)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(480, 360)" cx="30" cy="30" r="30" fill="#caf0f8"
            stroke="#48cae4" stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(480, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(480, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(480, 420) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#03045e" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(480, 480) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(480, 480) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(480, 480) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(480, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(480, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(480, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(540, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(540, 0) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#0077b6" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(540, 60) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 60) translate(30,30) rotate(0) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#0096c7"></path>
        <path transform="translate(540, 60) translate(30,30) rotate(0) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(540, 120) translate(30,30) rotate(90) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 120) translate(30,30) rotate(90) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#0096c7"></path>
        <path transform="translate(540, 120) translate(30,30) rotate(90) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(540, 180) translate(30,30) rotate(0) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 180) translate(30,30) rotate(0) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
        <path transform="translate(540, 180) translate(30,30) rotate(0) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(540, 240) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 240) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(540, 240) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(540, 300) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 300) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(540, 300) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#ade8f4" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
    <svg>
        <path transform="translate(540, 360) translate(30,30) rotate(270) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 360) translate(30,30) rotate(270) translate(-30,-30)"
            fill="#ade8f4" stroke="#48cae4" stroke-width="2"
            d="M0 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
    </svg>
    <svg>
        <path transform="translate(540, 420) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 420) translate(30,30) rotate(180) translate(-30,-30)"
            d="M0 30C0 48.05 15.3348 60 30 60V0C15.3348 0 0 14.9129 0 30Z" stroke="#48cae4"
            stroke-width="2" fill="#0077b6"></path>
        <path transform="translate(540, 420) translate(30,30) rotate(180) translate(-30,-30)"
            d="M60 30C60 14.9129 48.4719 0 30 0L30 60C48.4719 60 60 48.05 60 30Z" stroke="#48cae4"
            stroke-width="2" fill="none"></path>
    </svg>
    <svg>
        <path transform="translate(540, 480)" fill="none" stroke="#48cae4" stroke-width="2"
            d="M0 0h60v60h-60z"></path>
        <circle transform="translate(540, 480)" cx="30" cy="30" r="30" fill="none" stroke="#48cae4"
            stroke-width="2"></circle>
    </svg>
    <svg>
        <path transform="translate(540, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2" d="M0 0h60v60h-60z"></path>
        <path transform="translate(540, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0 16.569 13.432 30 30 30v-60c-16.568 0-30 13.432-30 30z"></path>
        <path transform="translate(540, 540) translate(30,30) rotate(180) translate(-30,-30)"
            fill="none" stroke="#48cae4" stroke-width="2"
            d="M30 30c0-16.57-13.431-30-30-30v60c16.569 0 30-13.432 30-30z"></path>
    </svg>
</svg>